package cn.savas.hub.module.system.api.auth;

import cn.savas.hub.framework.common.enums.UserTypeEnum;
import cn.savas.hub.framework.common.util.servlet.ServletUtils;
import cn.savas.hub.framework.websocket.core.sender.WebSocketMessageSender;
import cn.savas.hub.module.client.enums.system.AuthUserErrorEnum;
import cn.savas.hub.module.client.enums.system.CSocketMessageTypeEnum;
import cn.savas.hub.module.system.api.auth.dto.ClientAuthLoginRespDTO;
import cn.savas.hub.module.system.controller.admin.auth.vo.AuthLoginRespVO;
import cn.savas.hub.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import cn.savas.hub.module.system.enums.logger.LoginLogTypeEnum;
import cn.savas.hub.module.system.enums.oauth2.OAuth2ClientConstants;
import cn.savas.hub.module.system.service.auth.AdminAuthService;
import cn.savas.hub.module.system.service.oauth2.OAuth2TokenService;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

import static cn.savas.hub.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.savas.hub.module.system.enums.ErrorCodeConstants.AUTH_TOKEN_NOT_EXISTS;

/**
 * <AUTHOR>
 * @date 2024/10/24 16:25
 */
@Service
public class AdminAuthApiImpl implements AdminAuthApi{
    @Resource
    private AdminAuthService adminAuthService;
    @Resource
    private WebSocketMessageSender webSocketMessageSender;
    @Resource
    private OAuth2TokenService oauth2TokenService;

    @Override
    public ClientAuthLoginRespDTO clientLogin(String username, String password) {
        // 1. 用户认证
        ClientAuthLoginRespDTO respDTO = adminAuthService.clientAuthenticate(username, password, LoginLogTypeEnum.LOGIN_CLIENT);
        if (!respDTO.getValue().equals(AuthUserErrorEnum.LOGIN_SUCCESS.getCode())) {
            return respDTO;
        }

        // 2. 获取客户端在线用户列表
        List<OAuth2AccessTokenDO> onlineUserList = adminAuthService.getClientOnlineUserList(UserTypeEnum.DESKTOP.getValue());

        // 3. 检查当前用户是否已在线
        OAuth2AccessTokenDO existingToken = findUserOnlineToken(onlineUserList, respDTO.getUserId());
        if (existingToken != null) {
            return handleExistingOnlineUser(respDTO, existingToken);
        }

        // 4. 创建新的访问令牌
        return createNewUserToken(respDTO, username);
    }

    /**
     * 查找用户的在线令牌
     */
    private OAuth2AccessTokenDO findUserOnlineToken(List<OAuth2AccessTokenDO> onlineUserList, Long userId) {
        return onlineUserList.stream()
                .filter(token -> token.getUserId().equals(userId))
                .findFirst()
                .orElse(null);
    }

    /**
     * 处理已在线用户的登录逻辑
     */
    private ClientAuthLoginRespDTO handleExistingOnlineUser(ClientAuthLoginRespDTO respDTO, OAuth2AccessTokenDO existingToken) {
        String currentIp = ServletUtils.getClientIP();
        String existingIp = existingToken.getUserIp();

        // 判断是否为同一设备登录（IP地址相同）
        if (existingIp != null && existingIp.equals(currentIp)) {
            // 刷新令牌
            refreshUserToken(respDTO, existingToken);
        } else {
            // 不同设备登录，返回错误信息（安全考虑：不返回访问令牌）
            respDTO.setValue(AuthUserErrorEnum.USER_LOGGED_IN_ELSEWHERE.getCode());
            // 设置accessToken，客户端需要强制登录
            respDTO.setAccessToken(existingToken.getAccessToken());
        }
        return respDTO;
    }

    /**
     * 创建新用户令牌
     */
    private ClientAuthLoginRespDTO createNewUserToken(ClientAuthLoginRespDTO respDTO, String username) {
        AuthLoginRespVO tokenResult = adminAuthService.createTokenAfterLoginSuccess(
                respDTO.getUserId(),
                username,
                LoginLogTypeEnum.LOGIN_CLIENT,
                OAuth2ClientConstants.CLIENT_CLIENT_ID,
                UserTypeEnum.DESKTOP.getValue()
        );
        setTokenInfo(respDTO, tokenResult);
        return respDTO;
    }

    /**
     * 设置令牌信息到响应对象
     */
    private void refreshUserToken(ClientAuthLoginRespDTO respDTO, OAuth2AccessTokenDO existingToken) {
        OAuth2AccessTokenDO newToken = oauth2TokenService.refreshAccessToken(existingToken.getRefreshToken(), OAuth2ClientConstants.CLIENT_CLIENT_ID);
        if (newToken == null) {
            throw exception(AUTH_TOKEN_NOT_EXISTS);
        }
        respDTO.setAccessToken(newToken.getAccessToken());
        respDTO.setExpiresTime(newToken.getExpiresTime());
        respDTO.setRefreshToken(newToken.getRefreshToken());
    }

    /**
     * 设置令牌信息到响应对象（重载方法）
     */
    private void setTokenInfo(ClientAuthLoginRespDTO respDTO, AuthLoginRespVO tokenResult) {
        respDTO.setAccessToken(tokenResult.getAccessToken());
        respDTO.setExpiresTime(tokenResult.getExpiresTime());
        respDTO.setRefreshToken(tokenResult.getRefreshToken());
    }

    @Override
    public void clientLogout(String token) {
        adminAuthService.logout(token, LoginLogTypeEnum.LOGOUT_CLIENT_SELF.getType());
    }

    @Override
    public void clientForceLogout(String accessToken) {
        if (accessToken == null) {
            throw exception(AUTH_TOKEN_NOT_EXISTS);
        }
        OAuth2AccessTokenDO tokenDO = oauth2TokenService.getAccessToken(accessToken);
        if (tokenDO != null) {
            adminAuthService.logout(accessToken, LoginLogTypeEnum.LOGOUT_CLIENT_DELETE.getType());
            //通知
            webSocketMessageSender.send(UserTypeEnum.DESKTOP.getValue(), tokenDO.getUserId(), CSocketMessageTypeEnum.FORCE_LOGOUT.getCode(), "用户被强制下线");

        }
    }

}
