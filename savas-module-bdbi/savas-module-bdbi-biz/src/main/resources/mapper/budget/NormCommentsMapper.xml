<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.savas.hub.module.bdbi.dal.mapper.NormCommentsMapper">

    <insert id="addRemark">
        INSERT INTO bdbi_norm_comments (project_id, eng_id, table_data_id, comment)
        VALUES (#{reqVO.projectId}, #{reqVO.engId}, #{reqVO.tableDataId}, #{reqVO.comment, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler})
        ON DUPLICATE KEY UPDATE
        comment = JSON_MERGE_PATCH(comment,  #{reqVO.comment, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler});
    </insert>
</mapper>
