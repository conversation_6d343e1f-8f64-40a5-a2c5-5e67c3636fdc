package cn.savas.hub.module.bdbi.service.device.decorator;


import cn.savas.hub.module.bdbi.api.device.dto.DeviceComparisonTableHeaderDto;

import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 * 动态表格通用抽象类
 *
 * <AUTHOR>
 */
public abstract class DeviceComparisonTable {
    /**
     * 获取表头
     *
     * @return
     */
    abstract List<DeviceComparisonTableHeaderDto> getTableHeader();

    /**
     * 获取表数据
     *
     * @return
     */
    abstract List<Map<String, Object>> getTableData();

    protected static <T> T setHeaderChildren(T source, T target, Function<T, List<T>> function, BiConsumer<T, List<T>> consumer) {
        consumer.accept(source, function.apply(target));
        return target;
    }


}
