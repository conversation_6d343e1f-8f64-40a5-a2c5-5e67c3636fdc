package cn.savas.hub.module.bdbi.controller.admin.device.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * wbs树形实体
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "wbs树形实体")
public class WbsTreeResponseDto {

    @Schema(description = "Id")
    private String id;

    @Schema(description = "结构pid")
    private String pid;

    @Schema(description = "wbs名称")
    private String name;

    @Schema(description = "wbs类型")
    private Integer wbsType;

    @Schema(description = "图标")
    private String icon;

    /**
     * 费用缓存
     */
    private byte[] params;
    /**
     * 编号
     */
    private String code;

    @Schema(description = "子项")
    private List<WbsTreeResponseDto> children;
}
