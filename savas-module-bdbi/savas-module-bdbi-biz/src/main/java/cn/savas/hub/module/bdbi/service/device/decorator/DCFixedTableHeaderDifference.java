package cn.savas.hub.module.bdbi.service.device.decorator;

import cn.savas.hub.module.bdbi.api.device.dto.DeviceComparisonTableHeaderDto;
import cn.savas.hub.module.bdbi.controller.admin.device.vo.DevicePrjResponseDto;
import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR> zq
 * @description: 差值列表头
 * @date : 2022/4/24 14:23
 */
public class DCFixedTableHeaderDifference extends DCFixedTableDecorator {
    private static final int GROUP_MIN_WIDTH = 140;
    private final List<DevicePrjResponseDto> projectList;

    public DCFixedTableHeaderDifference(DeviceComparisonTable source, List<DevicePrjResponseDto> projectList) {
        super(source);
        this.projectList = projectList;
    }

    @Override
    public List<DeviceComparisonTableHeaderDto> getTableHeader() {
        List<DeviceComparisonTableHeaderDto> tableHeader = super.getTableHeader();
        String masterPrjName = CollectionUtils.isEmpty(projectList) ? "" : projectList.get(0).getPrjName();
        for (int i = 1; i < projectList.size(); i++) {
//            DevicePrjResponseDto devicePrjDto = projectList.get(i);
            DeviceComparisonTableHeaderDto difference1 = DeviceComparisonTableHeaderDto.builder().field("group1Difference" + i).title("合计差值" + "（A-" + (char) (65 + i) + "）").fixed("left").minWidth(GROUP_MIN_WIDTH).build();
            DeviceComparisonTableHeaderDto difference2 = setHeaderChildren(difference1, DeviceComparisonTableHeaderDto.builder().field("group2Difference" + i).title("").minWidth(GROUP_MIN_WIDTH).build(), Lists::newArrayList, DeviceComparisonTableHeaderDto::setChildren);
            DeviceComparisonTableHeaderDto difference3 = setHeaderChildren(difference2, DeviceComparisonTableHeaderDto.builder().field("group3Difference" + i).title("").minWidth(GROUP_MIN_WIDTH).build(), Lists::newArrayList, DeviceComparisonTableHeaderDto::setChildren);
            DeviceComparisonTableHeaderDto difference4 = setHeaderChildren(difference3, DeviceComparisonTableHeaderDto.builder().field("group4Difference" + i).title("").minWidth(GROUP_MIN_WIDTH).build(), Lists::newArrayList, DeviceComparisonTableHeaderDto::setChildren);
            DeviceComparisonTableHeaderDto difference5 = setHeaderChildren(difference4, DeviceComparisonTableHeaderDto.builder().field("group5Difference" + i).title("").minWidth(GROUP_MIN_WIDTH).build(), Lists::newArrayList, DeviceComparisonTableHeaderDto::setChildren);
            DeviceComparisonTableHeaderDto difference6 = setHeaderChildren(difference5, DeviceComparisonTableHeaderDto.builder().field("group6Difference" + i).title("").minWidth(GROUP_MIN_WIDTH).build(), Lists::newArrayList, DeviceComparisonTableHeaderDto::setChildren);
            DeviceComparisonTableHeaderDto difference7 = setHeaderChildren(difference6, DeviceComparisonTableHeaderDto.builder().field("group7Difference" + i).title("").minWidth(GROUP_MIN_WIDTH).build(), Lists::newArrayList, DeviceComparisonTableHeaderDto::setChildren);
            DeviceComparisonTableHeaderDto difference8 = setHeaderChildren(difference7, DeviceComparisonTableHeaderDto.builder().field("group8Difference" + i).title("").minWidth(GROUP_MIN_WIDTH).build(), Lists::newArrayList, DeviceComparisonTableHeaderDto::setChildren);
            setHeaderChildren(difference8, DeviceComparisonTableHeaderDto.builder().field("difference" + i).title("").minWidth(GROUP_MIN_WIDTH).build(), Lists::newArrayList, DeviceComparisonTableHeaderDto::setChildren);
            tableHeader.add(difference1);
        }
        return tableHeader;
    }
}
