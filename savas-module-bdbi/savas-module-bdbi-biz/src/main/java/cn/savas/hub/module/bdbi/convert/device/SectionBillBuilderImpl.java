package cn.savas.hub.module.bdbi.convert.device;


import cn.savas.hub.framework.common.client.enmus.EnumUnZipFieldName;
import cn.savas.hub.framework.common.client.util.V9BinaryToMapUtil;
import cn.savas.hub.module.bdbi.api.device.dto.DeviceComparisonResponseDto;
import cn.savas.hub.module.bdbi.controller.admin.device.vo.DevicePrjResponseDto;
import cn.savas.hub.module.bdbi.enums.device.CBSEnum;
import cn.savas.hub.module.client.api.project.dto.ClientEngineeringRespDTO;
import cn.savas.hub.module.collect.api.dto.PrjProjectCostDTO;
import cn.savas.hub.module.collect.api.dto.PrjSectionBillDTO;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class SectionBillBuilderImpl {
    private List<ClientEngineeringRespDTO> prjDwEngineeringList;
    private List<PrjSectionBillDTO> prjSectionBillList;
    private List<PrjProjectCostDTO> prjProjectCostList;
    private List<DevicePrjResponseDto> prjList;

    private final List<DeviceComparisonResponseDto> resultList = new ArrayList<>();

    /**
     * 添加单位数据
     * @param prjDwData
     */
    public void setDwData(List<ClientEngineeringRespDTO> prjDwData) {
        this.prjDwEngineeringList = prjDwData;
    }

    /**
     * 添加分部数据
     * @param prjFbData
     */
    public void setFbData(List<PrjSectionBillDTO> prjFbData) {
        this.prjSectionBillList = prjFbData;
    }

    /**
     * 添加工程费数据
     * @param prjProjectCostsList
     */
    public void setProjectCostsData(List<PrjProjectCostDTO> prjProjectCostsList) {
        this.prjProjectCostList = prjProjectCostsList;
    }

    /**
     * 添加项目列表
     * @param prjList
     */
    public void setProjectList(List<DevicePrjResponseDto> prjList) {
        this.prjList = prjList;
    }

    public List<DeviceComparisonResponseDto> getResult() {
        // 添加工程费行
        addEngineeringCost(resultList);
        // 单位和分部的数据和结构处理
        filterSpecialty(prjDwEngineeringList, resultList, source -> getDeviceComparisonResponseDto(prjSectionBillList, source), Collection::add);
        // 添加安全生产费
        addSafetyCost(resultList);
        return resultList;
    }


    /**
     * 添加安全生产费
     *
     * @param deviceComparisonResponseDtoList 结果集
     */
    private void addSafetyCost(List<DeviceComparisonResponseDto> deviceComparisonResponseDtoList) {
        for (DevicePrjResponseDto prjResponseDto : prjList) {
            PrjProjectCostDTO prjProjectCost = prjProjectCostList.stream().filter(e ->
                    e.getFeeHostmodel().equals(prjResponseDto.getSingleEngId()) && "安全生产费".equals(e.getFeeName())
            ).findFirst().orElseGet(PrjProjectCostDTO::new);
            DeviceComparisonResponseDto.Builder builder = unZipParametersPackageGcfDto(prjProjectCost.getFeeParameters());
            builder.deviceName(prjProjectCost.getFeeName());
            builder.proportion(getProportion(builder.getGcf()));
            builder.engProjectId(prjResponseDto.getEngId());
            builder.code(CBSEnum.SC.getValue());
            deviceComparisonResponseDtoList.add(builder.build());
        }
    }

    /**
     * 添加工程费
     *
     * @param deviceComparisonResponseDtoList 结果集
     */
    private void addEngineeringCost(List<DeviceComparisonResponseDto> deviceComparisonResponseDtoList) {
        for (DevicePrjResponseDto prjResponseDto : prjList) {
            PrjProjectCostDTO prjProjectCost = prjProjectCostList.stream().filter(e ->
                    e.getFeeHostmodel().equals(prjResponseDto.getSingleEngId()) && (EnumUnZipFieldName.GCF.getValue().equals(e.getFeeName())|| EnumUnZipFieldName.SHGCF.getValue().equals(e.getFeeName()))
            ).findFirst().orElseGet(PrjProjectCostDTO::new);
            DeviceComparisonResponseDto.Builder builder = unZipParametersPackageGcfDto(prjProjectCost.getFeeParameters());
            builder.deviceName(prjProjectCost.getFeeName());
            builder.proportion("100%");
            builder.engProjectId(prjResponseDto.getEngId());
            builder.code(CBSEnum.CC.getValue());
            deviceComparisonResponseDtoList.add(0, builder.build());
        }
    }

    /**
     * 单位和分部的数据和结构处理
     *
     * @param prjSectionBillList 全部的分部数据
     * @param source             当前单位对象
     * @return
     */
    private DeviceComparisonResponseDto getDeviceComparisonResponseDto(List<PrjSectionBillDTO> prjSectionBillList, ClientEngineeringRespDTO source) {
        // 单位数据
        DeviceComparisonResponseDto.Builder deviceComparisonResponseDto = unZipParametersPackageDto(source.getEngParameters(), (BigDecimal value) -> value.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));
        deviceComparisonResponseDto.deviceName(source.getEngName());
        deviceComparisonResponseDto.engProjectId(source.getEngProjectId());
        deviceComparisonResponseDto.engId(source.getEngId());
        deviceComparisonResponseDto.code(source.getEngCode());
        // 比例
        BigDecimal total = deviceComparisonResponseDto.getTotal();
        deviceComparisonResponseDto.proportion(getProportion(total));
        // 分部数据
        deviceComparisonResponseDto.children(deviceStructure(prjSectionBillList, source));
        return deviceComparisonResponseDto.build();
    }

    private String getProportion(BigDecimal dwTotal) {
        if (!CollectionUtils.isEmpty(resultList)) {
            BigDecimal dxGcf = resultList.get(0).getGcf();
            BigDecimal multiply;
            if ((BigDecimal.ZERO).compareTo(dxGcf) == 0) {
                multiply = dwTotal.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
            } else {
                BigDecimal divide = dwTotal.divide(dxGcf, 6, RoundingMode.HALF_UP);
                multiply = divide.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
            }
            return multiply + "%";
        }
        return "";
    }


    /**
     * 分部数据解压缩
     *
     * @param prjSectionBillList 分部数据
     * @return
     */
    private List<DeviceComparisonResponseDto> deviceStructure(List<PrjSectionBillDTO> prjSectionBillList, ClientEngineeringRespDTO source) {
        // 当前单位的分部数据
        List<PrjSectionBillDTO> deviceList = prjSectionBillList.stream().filter(prjSectionBillDto -> prjSectionBillDto.getBillHostmodel().equals(source.getEngId())).collect(Collectors.toList());
        List<DeviceComparisonResponseDto> resultDeviceList = new ArrayList<>();
        for (PrjSectionBillDTO prjSectionbill : deviceList) {
            DeviceComparisonResponseDto.Builder comparisonResponseDto = unZipParametersPackageDto(prjSectionbill.getBillParameters(), (BigDecimal value) -> value.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));
            comparisonResponseDto.deviceName(prjSectionbill.getBillName());
            comparisonResponseDto.zygcl(prjSectionbill.getBillExpression());
            comparisonResponseDto.engId(prjSectionbill.getBillHostmodel());
            comparisonResponseDto.engProjectId(prjSectionbill.getProjectId());
            comparisonResponseDto.code(source.getEngCode());
            resultDeviceList.add(comparisonResponseDto.build());
        }
        return resultDeviceList;
    }

    /**
     * 解压数据
     *
     * @param billParameters 压缩数据
     * @return
     */
    static DeviceComparisonResponseDto.Builder unZipParametersPackageDto(byte[] billParameters, Function<BigDecimal, BigDecimal> valueFunction) {
        Map<String, BigDecimal> billDoubleMap = V9BinaryToMapUtil.unpackData(billParameters, valueFunction);
        return new DeviceComparisonResponseDto.Builder()
                .sbgzf(billDoubleMap.get(EnumUnZipFieldName.SBGZF.getCode()))
                .jksbgzf(billDoubleMap.get(EnumUnZipFieldName.JKSBGZF.getCode()))
                .zyclf(billDoubleMap.get(EnumUnZipFieldName.ZCGZF.getCode()))
                .jkzyclf(billDoubleMap.get(EnumUnZipFieldName.JKZCGZF.getCode()))
                .azf(billDoubleMap.get(EnumUnZipFieldName.AZF.getCode()))
                .jzgcf(billDoubleMap.get(EnumUnZipFieldName.JZGCF.getCode()))
                .gcf(billDoubleMap.get(EnumUnZipFieldName.GCF.getCode()))
                .wbf(billDoubleMap.get(EnumUnZipFieldName.WBF.getCode()));
    }

    /**
     * 解压数据工程费
     *
     * @param billParameters 压缩数据
     * @return
     */
    static DeviceComparisonResponseDto.Builder unZipParametersPackageGcfDto(byte[] billParameters) {
        Map<String, BigDecimal> billDoubleMap = V9BinaryToMapUtil.unpackData(billParameters, (BigDecimal value) -> value.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));
        return new DeviceComparisonResponseDto.Builder()
                .sbgzf(billDoubleMap.get(EnumUnZipFieldName.COST_SBF.getCode()))
                .zyclf(billDoubleMap.get(EnumUnZipFieldName.COST_ZCF.getCode()))
                .azf(billDoubleMap.get(EnumUnZipFieldName.COST_AZF.getCode()))
                .jzgcf(billDoubleMap.get(EnumUnZipFieldName.COST_JZF.getCode()))
                .gcf(billDoubleMap.get(EnumUnZipFieldName.GCF.getCode()))
                .wbf(billDoubleMap.get(EnumUnZipFieldName.COST_WBF.getCode()));
    }

    /**
     *
     */
    static <T, R> void filterSpecialty(Collection<T> sourceList, Collection<R> targetList, Function<T, R> function, BiConsumer<Collection<R>, R> consumer) {
        for (T u : sourceList) {
            R apply = function.apply(u);
            consumer.accept(targetList, apply);
        }
    }

}
