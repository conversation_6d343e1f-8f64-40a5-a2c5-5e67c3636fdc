package cn.savas.hub.module.bdbi.service.device;


import cn.savas.hub.framework.common.client.enmus.ENGTypeEnum;
import cn.savas.hub.framework.common.pojo.PageResult;
import cn.savas.hub.framework.common.util.object.BeanUtils;
import cn.savas.hub.framework.security.core.util.SecurityFrameworkUtils;
import cn.savas.hub.module.bdbi.api.device.dto.DeviceComparisonResponseDto;
import cn.savas.hub.module.bdbi.api.device.dto.DeviceComparisonTableHeaderDto;
import cn.savas.hub.module.bdbi.controller.admin.device.vo.*;
import cn.savas.hub.module.bdbi.convert.device.SectionBillBuilderImpl;
import cn.savas.hub.module.bdbi.enums.device.ProjectOverviewEnum;
import cn.savas.hub.module.bdbi.service.device.decorator.*;
import cn.savas.hub.module.client.api.project.ClientProjectApi;
import cn.savas.hub.module.client.api.project.ClientProjectFileApi;
import cn.savas.hub.module.client.api.project.dto.ClientEngineeringRespDTO;
import cn.savas.hub.module.client.api.project.dto.ClientProjectReqDTO;
import cn.savas.hub.module.client.api.project.dto.ClientProjectRespDTO;
import cn.savas.hub.module.collect.api.CollectDataApi;
import cn.savas.hub.module.collect.api.dto.PrjDeviceProjectDTO;
import cn.savas.hub.module.collect.api.dto.PrjProjectCostDTO;
import cn.savas.hub.module.collect.api.dto.PrjSectionBillDTO;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 多项目装置对比查询服务实现类
 *
 * <AUTHOR>
 */
@Service
public class DeviceComparisonServiceImpl implements DeviceComparisonService {
    @Resource
    private DeviceComparisonService deviceComparisonService;
    @Resource
    private ClientProjectApi clientProjectApi;
    @Resource
    private ClientProjectFileApi clientProjectFileApi;
    @Resource
    private CollectDataApi collectDataApi;
    @Override
    public List<DeviceComparisonResponseDto> getSectionBillList(SectionBillQuery sectionBillQuery) {
        String deviceName = sectionBillQuery.getDeviceName();
        List<DevicePrjResponseDto> prjList = sectionBillQuery.getPrjList();
        List<Long> prjIdList = sectionBillQuery.getPrjIdList();
        // 1.获取工程数据
        List<ClientEngineeringRespDTO> prjEngineeringList = clientProjectApi.getDeviceEngByPrjId(prjIdList);
        // 2.获取单项数据
        List<Long> dxEngIdList;
        if (!CollectionUtils.isEmpty(prjList)) {
            dxEngIdList = prjList.stream().map(DevicePrjResponseDto::getSingleEngId).collect(Collectors.toList());
        } else {
            dxEngIdList = prjEngineeringList.stream().filter(prjEngineering -> prjEngineering.getEngClass().equals(ENGTypeEnum.DANXIANG.getCode())
                    && prjEngineering.getEngName().contains(deviceName)).map(ClientEngineeringRespDTO::getEngId).collect(Collectors.toList());
        }
        // 3.获取单位数据
        List<ClientEngineeringRespDTO> prjDwEngineeringList = prjEngineeringList.stream().filter(prjEngineering -> dxEngIdList.contains(prjEngineering.getEngPid())).collect(Collectors.toList());
        // 4.获取分部数据、工程费用数据
        List<PrjSectionBillDTO> prjSectionBillList = new ArrayList<>();
        List<PrjProjectCostDTO> prjProjectCosts = new ArrayList<>();
        Map<Long, List<Long>> prjOrEngIdMap = prjDwEngineeringList.stream().collect(Collectors.groupingBy(ClientEngineeringRespDTO::getEngProjectId, Collectors.mapping(ClientEngineeringRespDTO::getEngId, Collectors.toList())));
        // 4.1 遍历所有项目文件获取分部数据、工程费用数据
        for (Map.Entry<Long, List<Long>> entryMap : prjOrEngIdMap.entrySet()) {
            Long prjId = entryMap.getKey();
            clientProjectFileApi.changePrjFileSumDs(prjId, ()->{
                List<Long> engIdList = entryMap.getValue();
                // 4.2 获取分部数据
                List<PrjSectionBillDTO> billByEngId = collectDataApi.getDeviceSectionBillByEngId(prjId, engIdList);
                billByEngId.forEach(e->e.setProjectId(prjId));
                prjSectionBillList.addAll(billByEngId);
                // 4.3 获取工程费用数据
                List<PrjProjectCostDTO> costByEngId = collectDataApi.getDeviceProjectCostByEngId(prjId, engIdList);
                costByEngId.forEach(e->e.setProjectId(prjId));
                prjProjectCosts.addAll(costByEngId);
                return null;
            });
        }
        SectionBillBuilderImpl sectionBillBuilder = new SectionBillBuilderImpl();
        sectionBillBuilder.setDwData(prjDwEngineeringList);
        sectionBillBuilder.setFbData(prjSectionBillList);
        sectionBillBuilder.setProjectCostsData(prjProjectCosts);
        sectionBillBuilder.setProjectList(prjList);
        return sectionBillBuilder.getResult();
    }

    @Override
    public List<DevicePrjResponseDto> getPrjList(PrjListQuery prjListQuery) {
        List<DevicePrjResponseDto> resultList = Lists.newArrayList();
        // 获取负责人项目列表
        List<ClientProjectRespDTO> projectList = clientProjectApi.getProjectList(new ClientProjectReqDTO(SecurityFrameworkUtils.getLoginUserId()));
        Set<Long> projectIdSet = projectList.stream().map(ClientProjectRespDTO::getProjectId).collect(Collectors.toSet());
        // 查找相关单项
        List<ClientEngineeringRespDTO> deviceEngByName = clientProjectApi.getDeviceEngByName(prjListQuery.getDeviceName(), projectIdSet);
        // 获取项目信息
        Map<Long, PrjDeviceProjectDTO> deviceProjectMap = new HashMap<>();
        deviceEngByName.stream().map(ClientEngineeringRespDTO::getEngProjectId).distinct().forEach(prjId -> {
            clientProjectFileApi.changePrjFileSumDs(prjId, () -> {
                deviceProjectMap.put(prjId, collectDataApi.getDeviceProject(prjId));
                return null;
            });
        });

        for (ClientEngineeringRespDTO prjEngineering : deviceEngByName) {
            PrjDeviceProjectDTO devicePrj = deviceProjectMap.get(prjEngineering.getEngProjectId());
            if (devicePrj==null) continue;
            DevicePrjResponseDto build = DevicePrjResponseDto.builder()
                    .engId(devicePrj.getProjectId())
                    .singleEngId(prjEngineering.getEngId())
                    .prjName(devicePrj.getProjectName())
                    .singleName(prjEngineering.getEngName())
                    .designStage(devicePrj.getDesignStage())
                    .priceLevel(devicePrj.getPriceLevel())
                    .createTime(devicePrj.getCreateTime())
                    .scale(DCFixedTableHeaderProject.getDesignStage(prjEngineering.getEngName()))
                    .currencyRate(ProjectOverviewEnum.CURRENCY_RATE.getValue())
                    .monetaryUnit(ProjectOverviewEnum.MONETARY_UNIT.getValue())
                    .includedVAT(ProjectOverviewEnum.INCLUDED_VAT.getValue()).build();
            resultList.add(build);
        }

        resultList.sort(Comparator.comparing(DevicePrjResponseDto::getPrjName));
        return resultList;
    }



    @Override
    public List<DeviceComparisonTableHeaderDto> getDynamicTableHead(DynamicTableQuery dynamicTableQuery) {
        List<DevicePrjResponseDto> prjList = dynamicTableQuery.getPrjList();
        DCFixedTableDecorator dcFixedTableHeaderProjectHeader = new DCFixedTableHeaderProject(new DCFixedTableHeaderDifference(new DCFixedTableHeader(Lists.newArrayList()), prjList), prjList);
        return dcFixedTableHeaderProjectHeader.getTableHeader();
    }

    @Override
    public List<Map<String, Object>> getDynamicTableData(DynamicTableQuery dynamicTableQuery) {
        List<Long> projectIdList = dynamicTableQuery.getPrjIdList();
        String singleProjectName = dynamicTableQuery.getSingleName();
        List<DeviceComparisonResponseDto> sectionBillList = deviceComparisonService.getSectionBillList(new SectionBillQuery(projectIdList, singleProjectName, dynamicTableQuery.getPrjList()));
        DCFixedTableDecorator dcFixedTableData = new DCFixedTableDataDifference(new DCFixedTableDataSequence(new DCFixedTableData(projectIdList, sectionBillList)), projectIdList);
        List<Map<String, Object>> resultList = dcFixedTableData.getTableData();
//        resultList.sort(Comparator.comparing(o -> CBSEnum.toMap.getOrDefault((String) o.get("code"), 0)));
        return resultList;
    }

    @Override
    public PageResult<DeviceComparisonPageRespVO> getProjectPage(DeviceComparisonPageReqVO pageReqVO) {
        // 构建查询对象
        ClientProjectReqDTO pageQuery = new ClientProjectReqDTO();
        pageQuery.setPageNo(pageReqVO.getPageNo());
        pageQuery.setPageSize(pageReqVO.getPageSize());
        pageQuery.setDirectorId(SecurityFrameworkUtils.getLoginUserId());
        PageResult<ClientProjectRespDTO> pageResult = clientProjectApi.getProjectPageList(pageQuery);
        // 构建返回对象
        return BeanUtils.toBean(pageResult, DeviceComparisonPageRespVO.class);
    }
}
