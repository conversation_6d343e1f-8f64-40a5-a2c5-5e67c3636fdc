package cn.savas.hub.module.bdbi.controller.admin.project.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/25 16:27
 */
@Data
@Builder
public class OtherFeesRespVO {
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "上级id")
    private Long pid;

    @Schema(description = "序号")
    private String indexNo;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "计算依据")
    private String calculationBasis;

    @Schema(description = "计算式")
    private String calculationFormula;

    @Schema(description = "其他费金额")
    private BigDecimal projectOtherAmount;

    @Schema(description = "外币金额")
    private BigDecimal projectForeignAmount;

    @Schema(description = "增值税率")
    private String vatRate;

    @Schema(description = "增值税")
    private BigDecimal vat;

    @Schema(description = "说明")
    private String instruction;

    @Schema(description = "子类")
    private List<OtherFeesRespVO> children;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "费率")
    private BigDecimal rate;

    @Schema(description = "费用状态")
    private Long feeState;
}
