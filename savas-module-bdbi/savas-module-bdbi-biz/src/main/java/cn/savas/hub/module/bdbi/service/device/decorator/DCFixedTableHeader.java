package cn.savas.hub.module.bdbi.service.device.decorator;

import cn.savas.hub.module.bdbi.api.device.dto.DeviceComparisonTableHeaderDto;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR> zq
 * @description: 固定列表头
 * @date : 2022/4/24 14:23
 */
public class DCFixedTableHeader extends DeviceComparisonTable {
    private static final int MIN_WIDTH = 160;
    private final List<DeviceComparisonTableHeaderDto> deviceComparisonTableHeaderDtoList;

    public DCFixedTableHeader(List<DeviceComparisonTableHeaderDto> deviceComparisonTableHeaderDtoList) {
        this.deviceComparisonTableHeaderDtoList = deviceComparisonTableHeaderDtoList;
    }

    @Override
    public List<DeviceComparisonTableHeaderDto> getTableHeader() {
        // 创建序号列
        deviceComparisonTableHeaderDtoList.add(createSequenceColumn());
        // 创建项目表头列
        deviceComparisonTableHeaderDtoList.add(createProjectColumn());
        return deviceComparisonTableHeaderDtoList;
    }

    @Override
    List<Map<String, Object>> getTableData() {
        return null;
    }

    /**
     * 创建项目表头列
     */
    protected DeviceComparisonTableHeaderDto createProjectColumn() {
        DeviceComparisonTableHeaderDto projectName = DeviceComparisonTableHeaderDto.builder().field("projectName").title("项目（工厂）名称").fixed("left").minWidth(MIN_WIDTH).build();
        DeviceComparisonTableHeaderDto dxName = setHeaderChildren(projectName, DeviceComparisonTableHeaderDto.builder().field("dxName").title("单元（主项）名称").minWidth(MIN_WIDTH).build(), Lists::newArrayList, DeviceComparisonTableHeaderDto::setChildren);
        DeviceComparisonTableHeaderDto deviceComparisonTableHeaderDto = setHeaderChildren(dxName, DeviceComparisonTableHeaderDto.builder().field("designStage").title("设计阶段").minWidth(MIN_WIDTH).build(), Lists::newArrayList, DeviceComparisonTableHeaderDto::setChildren);
        DeviceComparisonTableHeaderDto scale = setHeaderChildren(deviceComparisonTableHeaderDto, DeviceComparisonTableHeaderDto.builder().field("scale").title("规模（*系列）").minWidth(MIN_WIDTH).build(), Lists::newArrayList, DeviceComparisonTableHeaderDto::setChildren);
        DeviceComparisonTableHeaderDto priceLevel = setHeaderChildren(scale, DeviceComparisonTableHeaderDto.builder().field("priceLevel").title("价格水平").minWidth(MIN_WIDTH).build(), Lists::newArrayList, DeviceComparisonTableHeaderDto::setChildren);
        DeviceComparisonTableHeaderDto currencyRate = setHeaderChildren(priceLevel, DeviceComparisonTableHeaderDto.builder().field("currencyRate").title("美元汇率").minWidth(MIN_WIDTH).build(), Lists::newArrayList, DeviceComparisonTableHeaderDto::setChildren);
        DeviceComparisonTableHeaderDto monetaryUnit = setHeaderChildren(currencyRate, DeviceComparisonTableHeaderDto.builder().field("monetaryUnit").title("货币单位").minWidth(MIN_WIDTH).build(), Lists::newArrayList, DeviceComparisonTableHeaderDto::setChildren);
        DeviceComparisonTableHeaderDto isIncludedVat = setHeaderChildren(monetaryUnit, DeviceComparisonTableHeaderDto.builder().field("isIncludedVAT").title("是否含增值税").minWidth(MIN_WIDTH).build(), Lists::newArrayList, DeviceComparisonTableHeaderDto::setChildren);
        setHeaderChildren(isIncludedVat, DeviceComparisonTableHeaderDto.builder().field("deviceName").title("").treeNode(Boolean.TRUE).minWidth(MIN_WIDTH).build(), Lists::newArrayList, DeviceComparisonTableHeaderDto::setChildren);
        return projectName;
    }

    /**
     * 创建序号列
     */
    protected DeviceComparisonTableHeaderDto createSequenceColumn() {
        return setHeaderChildren(DeviceComparisonTableHeaderDto.builder().field("sequence1").title("序号").minWidth(50).fixed("left").build(), 2, 9);
    }

    protected DeviceComparisonTableHeaderDto setHeaderChildren(DeviceComparisonTableHeaderDto deviceComparisonTableHeaderDto, int index, int endIndex) {
        DeviceComparisonTableHeaderDto.DeviceComparisonTableHeaderDtoBuilder builder = DeviceComparisonTableHeaderDto.builder();
        if (index < endIndex) {
            builder.field("sequence" + index);
            builder.title("");
            builder.minWidth(deviceComparisonTableHeaderDto.getMinWidth());
            deviceComparisonTableHeaderDto.setChildren(Lists.newArrayList(setHeaderChildren(builder.build(), index + 1, endIndex)));
        }else if(index == endIndex){
            deviceComparisonTableHeaderDto.setChildren(Lists.newArrayList(DeviceComparisonTableHeaderDto.builder().field("sequence").title("").minWidth(deviceComparisonTableHeaderDto.getMinWidth()).build()));
        }
        return deviceComparisonTableHeaderDto;
    }
}
