package cn.savas.hub.module.bdbi.controller.admin.project.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/18 17:57
 */
@Data
public class WbsTreeRespVO {
    @Schema(description = "Id")
    private String id;

    @Schema(description = "结构pid")
    private String pid;

    @Schema(description = "wbs名称")
    private String name;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "wbs类型")
    private Long wbsType;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "子节点")
    private List<WbsTreeRespVO> children;
}
