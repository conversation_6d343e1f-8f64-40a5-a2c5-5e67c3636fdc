package cn.savas.hub.module.bdbi.service.project;

import cn.savas.hub.framework.common.client.enmus.ClientClassIdEnum;
import cn.savas.hub.framework.common.util.collection.TreeDataUtil;
import cn.savas.hub.module.bdbi.api.budgetestimate.dto.NormSearchRespDTO;
import cn.savas.hub.module.bdbi.controller.admin.project.vo.NormSearchReqVO;
import cn.savas.hub.module.bdbi.convert.project.BudgetEstimateConvert;
import cn.savas.hub.module.bdbi.enums.budget.ChildTypeEnum;
import cn.savas.hub.module.collect.api.dto.PrjEngineeringDTO;
import cn.savas.hub.module.collect.api.dto.PrjNormDTO;
import cn.savas.hub.module.collect.api.dto.PrjSectionBillDTO;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/15 16:28
 */
@Service
public class NormDataService {
    /**
     * 专业数据
     *
     * @param reqVO
     */
    public List<NormSearchRespDTO> singleProjectOrProject(NormSearchReqVO reqVO,
                                                          List<PrjEngineeringDTO> prjEngineeringList,
                                                          List<PrjSectionBillDTO> prjSectionBills,
                                                          List<PrjNormDTO> prjNorms) {
        List<NormSearchRespDTO> resultData = Lists.newArrayList();
        switch (Objects.requireNonNull(ClientClassIdEnum.getEnumByCode(reqVO.getEngClass()))) {
            // 执行到单项终止
            case PROJECT:
            case SECTIONSUBITEM:
            case SECTIONENG:
                List<PrjEngineeringDTO> collect = prjEngineeringList.stream().filter(e -> e.getEngPid().equals(reqVO.getEngId())).collect(Collectors.toList());
                for (PrjEngineeringDTO prjEngineering : collect) {
                    reqVO.setEngId(prjEngineering.getEngId());
                    reqVO.setEngClass(prjEngineering.getEngClass());

                    // 转化添加单位
                    NormSearchRespDTO childResponseDto = BudgetEstimateConvert.INSTANCE.convert6(prjEngineering);
                    // 设置返回数据类型
                    childResponseDto.setEngClass(prjEngineering.getEngClass());
                    // 设置类型
                    ClientClassIdEnum engTypeEnum = ClientClassIdEnum.getEnumByCode(prjEngineering.getEngClass());
                    childResponseDto.setNormTypeName(engTypeEnum == null ? "" : engTypeEnum.getName());
                    List<NormSearchRespDTO> singleProjectOrProjectList = singleProjectOrProject(reqVO, prjEngineeringList, prjSectionBills, prjNorms);
                    if (!CollectionUtils.isEmpty(singleProjectOrProjectList)) {
                        childResponseDto.setChildren(singleProjectOrProjectList);
                        resultData.add(childResponseDto);
                    }
                }
                break;
            case UNITENG:
                List<NormSearchRespDTO> resultList = Lists.newArrayList();
                // 获取分部
                List<PrjSectionBillDTO> prjSectionBillList = prjSectionBills.stream().
                        filter(e -> e.getBillHostmodel().equals(reqVO.getEngId()) &&
                                e.getBillProfessionalcode().equals(reqVO.getProfessionalCode())).
                        filter(e -> StringUtils.isBlank(reqVO.getFbName()) ? Boolean.TRUE : e.getBillName().contains(reqVO.getFbName())).
                        collect(Collectors.toList());
                List<Long> billIdList = prjSectionBillList.stream().map(PrjSectionBillDTO::getBillId).collect(Collectors.toList());
                // 转化添加分部
                resultList.addAll(BudgetEstimateConvert.INSTANCE.convertList7(prjSectionBillList));
                // 获取子目
                List<PrjNormDTO> normList = prjNorms.stream().filter(e -> billIdList.contains(e.getNormPid())).collect(Collectors.toList());
                // 转化添加子目
                resultList.addAll(BudgetEstimateConvert.INSTANCE.convertList8(normList));
                List<NormSearchRespDTO> childResponseDtoListTree = TreeDataUtil.toTreeNoParentAdd(resultList, NormSearchRespDTO::getStructureId, NormSearchRespDTO::getStructurePid, NormSearchRespDTO::setChildren, NormSearchRespDTO::getChildren);
                //删除空分部
                emptyChildrenRm(childResponseDtoListTree);
                return childResponseDtoListTree;
            default:
                return Collections.emptyList();
        }
        return resultData;
    }

    private void emptyChildrenRm(List<NormSearchRespDTO> childResponseDtoListTree) {
        if (childResponseDtoListTree != null) {
            Iterator<NormSearchRespDTO> iterator = childResponseDtoListTree.iterator();
            while (iterator.hasNext()) {
                NormSearchRespDTO next = iterator.next();
                if (Objects.equals(next.getNormType(), ChildTypeEnum.SUBSECTION.getCode())) {
                    emptyChildrenRm(next.getChildren());
                    if (CollectionUtils.isEmpty(next.getChildren())) {
                        iterator.remove();
                    }
                }
            }
        }
    }

}
