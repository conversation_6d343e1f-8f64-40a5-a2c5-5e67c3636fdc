package cn.savas.hub.module.bdbi.service.device;


import cn.savas.hub.framework.common.pojo.PageResult;
import cn.savas.hub.module.bdbi.api.device.dto.DeviceComparisonResponseDto;
import cn.savas.hub.module.bdbi.api.device.dto.DeviceComparisonTableHeaderDto;
import cn.savas.hub.module.bdbi.controller.admin.device.vo.*;

import java.util.List;
import java.util.Map;

/**
 * 多项目装置对比查询服务类
 * <AUTHOR>
 */
public interface DeviceComparisonService {

    /**
     * 获取项目列表
     */
    List<DevicePrjResponseDto> getPrjList(PrjListQuery prjListQuery);

    /**
     * 根据项目id和单项名称获取单位和分部列表
     */
    List<DeviceComparisonResponseDto> getSectionBillList(SectionBillQuery sectionBillQuery);

    /**
     * 获取动态表头
     */
    List<DeviceComparisonTableHeaderDto> getDynamicTableHead(DynamicTableQuery dynamicTableQuery);

    /**
     * 获取动态数据
     */
    List<Map<String, Object>> getDynamicTableData(DynamicTableQuery dynamicTableQuery);

    /**
     * 获取项目分页
     * @param pageReqVO
     * @return
     */
    PageResult<DeviceComparisonPageRespVO> getProjectPage(DeviceComparisonPageReqVO pageReqVO);
}
