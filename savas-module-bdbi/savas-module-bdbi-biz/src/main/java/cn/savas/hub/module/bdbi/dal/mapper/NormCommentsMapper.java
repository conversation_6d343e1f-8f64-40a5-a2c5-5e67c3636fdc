package cn.savas.hub.module.bdbi.dal.mapper;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.module.bdbi.controller.admin.project.vo.NormCommentReqVO;
import cn.savas.hub.module.bdbi.dal.dataobject.NormCommentsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2024/11/25 11:33
 */
@Mapper
public interface NormCommentsMapper extends BaseMapperX<NormCommentsDO> {

    void addRemark(@Param("reqVO") NormCommentReqVO reqVO);
}
