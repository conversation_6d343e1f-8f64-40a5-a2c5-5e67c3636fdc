package cn.savas.hub.module.bdbi.controller.admin.project.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/5/15 14:30
 */
@Data
public class NormSearchReqVO {
    @Schema(description ="项目id")
    @NotNull(message = "项目id不能为空")
    private Long projectId;

    @Schema(description ="工程ID")
    @NotNull(message = "工程ID不能为空")
    private Long engId;

    @Schema(description ="专业 构筑物，静置设备,给排水....")
    private String specialty;

    @Schema(description ="专业归类 建筑:Architecture，安装:Installtion")
    private String professionalCode;

    @Schema(description ="工程类型")
    private Long engClass;

    @Schema(description ="子目名称")
    private String normLike;

    @Schema(description ="单位名称")
    private String dwName;

    @Schema(description ="材质名称")
    private String czName;

    @Schema(description ="CBS分部名称")
    private String fbName;

    @Schema(description ="位号名称")
    private String whName;


    @Override
    public NormSearchReqVO clone() {
        NormSearchReqVO reqVO = new NormSearchReqVO();
        reqVO.setEngId(engId);
        reqVO.setSpecialty(specialty);
        reqVO.setProfessionalCode(professionalCode);
        reqVO.setEngClass(engClass);
        reqVO.setProjectId(projectId);
        reqVO.setNormLike(normLike);
        reqVO.setDwName(dwName);
        reqVO.setCzName(czName);
        reqVO.setFbName(fbName);
        reqVO.setWhName(whName);
        return reqVO;
    }
}
