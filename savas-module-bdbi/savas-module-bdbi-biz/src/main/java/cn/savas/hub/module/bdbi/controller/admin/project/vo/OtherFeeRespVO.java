package cn.savas.hub.module.bdbi.controller.admin.project.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/12 13:58
 */
@Data
public class OtherFeeRespVO {

    @Schema(description = "费用ID")
    private Long id;

    @Schema(description = "父级费用ID")
    private Long pid;

    @Schema(description = "主模型ID")
    private Long feeHostmodel;

    @Schema(description = "序号")
    private String feeSequence;

    @Schema(description = "名称")
    private String feeName;

    @Schema(description = "编码")
    private String feeCode;

    @Schema(description = "计算方式")
    private String feeCostTag;

    @Schema(description = "计算式")
    private String feeExpression;

    @Schema(description = "费率")
    private BigDecimal feeRate;

    @Schema(description = "金额(万元)")
    private BigDecimal feeValue;

    @Schema(description = "外币金额(万元)")
    private BigDecimal feeForeignValue;

    @Schema(description = "税率")
    private BigDecimal feeTaxrate;

    @Schema(description = "增值税(万元)")
    private BigDecimal feeTax;

    @Schema(description = "单价公式")
    private String feeExpense;

    @Schema(description = "计算依据")
    private String feeCalculatorbasis;

    private List<OtherFeeRespVO> children;
}
