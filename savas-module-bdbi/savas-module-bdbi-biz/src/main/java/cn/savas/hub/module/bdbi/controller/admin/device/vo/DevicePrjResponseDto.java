package cn.savas.hub.module.bdbi.controller.admin.device.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DevicePrjResponseDto {

    @Schema(description = "项目编号")
    private Long engId;

    @Schema(description = "项目名称")
    private String prjName;

    @Schema(description = "单项名称")
    private String singleName;

    @Schema(description = "单项Id")
    private Long singleEngId;

    @Schema(description = "设计阶段")
    private String designStage;

    @Schema(description = "设计时间")
    private String designDate;

    @Schema(description = "规模")
    private String scale;

    @Schema(description = "价格水平")
    private String priceLevel;

    @Schema(description = "美元汇率")
    private String currencyRate;

    @Schema(description = "货币单位")
    private String monetaryUnit;

    @Schema(description = "是否含增值税")
    private String includedVAT;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}
