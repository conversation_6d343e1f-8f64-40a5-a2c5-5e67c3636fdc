package cn.savas.hub.module.bdbi.convert.project;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.savas.hub.framework.common.client.enmus.ENGTypeEnum;
import cn.savas.hub.framework.common.client.enmus.EnumUnZipFieldName;
import cn.savas.hub.framework.common.client.enmus.EnumUnZipFieldNameV9;
import cn.savas.hub.framework.common.client.util.V9BinaryToMapUtil;
import cn.savas.hub.module.bdbi.controller.admin.project.vo.*;
import cn.savas.hub.framework.common.client.enmus.BillProfessionEnum;
import cn.savas.hub.module.bdbi.enums.budget.ChildTypeEnum;
import cn.savas.hub.module.client.api.project.dto.ClientProjectRespDTO;
import cn.savas.hub.module.collect.api.dto.*;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> TODO 概算查看测试通过后需要将这个类中的 EnumUnZipFieldName 替换成 EnumUnZipFieldNameV9
 * @date 2024/11/15 18:24
 */
@Mapper
public interface ProjectConvert {
    ProjectConvert INSTANCE = Mappers.getMapper(ProjectConvert.class);
    default BigDecimal unZipDataToDto(EnumUnZipFieldName name, Map<String, BigDecimal> unzipMap) {
        return unzipMap.containsKey(name.getCode()) ? unzipMap.get(name.getCode()) : BigDecimal.ZERO;
    }

    default BigDecimal unZipDataToDto(EnumUnZipFieldNameV9 name, Map<String, BigDecimal> unzipMap) {
        return unzipMap.containsKey(name.getCode()) ? unzipMap.get(name.getCode()) : BigDecimal.ZERO;
    }
    /**
     * 概算分页
     */
    @Mappings({
            @Mapping(target= "projectName", source = "name"),
            @Mapping(target= "projectCode", source = "code"),
    })
    BudgetEstimatePageRespVO convert(ClientProjectRespDTO bean);
    List<BudgetEstimatePageRespVO> convertList(List<ClientProjectRespDTO> bean);

    /**
     * 工程结构（wbs）
     */
    WbsTreeRespVO convert1(WbsTreeDTO bean);
    List<WbsTreeRespVO> convertList1(List<WbsTreeDTO> bean);

    /**
     * 工程概况
     */
    BudgetProjectInfoRespVO convert2(BudgetProjectInfoDTO bean);
    List<BudgetProjectInfoRespVO> convertList2(List<BudgetProjectInfoDTO> bean);

    /**
     * 项目总概算-工程费转换
     */
    @Named("convert3")
    default EngineeringFeesRespVO convert3(PrjProjectCostDTO bean){
        Map<String, BigDecimal> unzipMap = V9BinaryToMapUtil.unpackData(bean.getFeeParameters(), (BigDecimal value) -> value.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));
        EngineeringFeesRespVO.EngineeringFeesRespVOBuilder responseDtoBuilder = EngineeringFeesRespVO.builder();
        responseDtoBuilder.id(bean.getFeeId());
        responseDtoBuilder.pid(bean.getFeePid());
        responseDtoBuilder.feeCode(bean.getFeeCode());
        responseDtoBuilder.indexNo(bean.getFeeSequence());

        responseDtoBuilder.name(bean.getFeeName());

        responseDtoBuilder.emAmount(NumberUtil.add(unZipDataToDto(EnumUnZipFieldNameV9.SBGZF, unzipMap), unZipDataToDto(EnumUnZipFieldNameV9.JK_SBGZF, unzipMap)));
        responseDtoBuilder.mainMaterialAmount(NumberUtil.add(unZipDataToDto(EnumUnZipFieldNameV9.ZYCLF, unzipMap), unZipDataToDto(EnumUnZipFieldNameV9.JK_ZYCLF, unzipMap)));
        responseDtoBuilder.installAmount(unZipDataToDto(EnumUnZipFieldNameV9.AZ_ZHF, unzipMap));
        responseDtoBuilder.projectBuildAmount(unZipDataToDto(EnumUnZipFieldNameV9.AZ_ZHF, unzipMap));
        responseDtoBuilder.projectTotalAmount(unZipDataToDto(EnumUnZipFieldNameV9.FEE, unzipMap));
        if (!"F000".equals(bean.getFeeCode()) || !"工程建设总概算".equals(bean.getFeeName())) {
            responseDtoBuilder.vat(unZipDataToDto(EnumUnZipFieldNameV9.ZZSF, unzipMap));
            BigDecimal taxGCF = unZipDataToDto(EnumUnZipFieldNameV9.FEE, unzipMap).add(unZipDataToDto(EnumUnZipFieldNameV9.ZZSF, unzipMap)).add(unZipDataToDto(EnumUnZipFieldNameV9.QF_ZZSHJ, unzipMap));
            responseDtoBuilder.vatTotal(taxGCF);
        }else {
            responseDtoBuilder.vatTotal(unZipDataToDto(EnumUnZipFieldNameV9.FEE, unzipMap));
        }

        responseDtoBuilder.projectForeignAmount(NumberUtil.add(unZipDataToDto(EnumUnZipFieldNameV9.WBF, unzipMap), unZipDataToDto(EnumUnZipFieldNameV9.WBSBF, unzipMap), unZipDataToDto(EnumUnZipFieldNameV9.WBZCF, unzipMap)));

        responseDtoBuilder.projectOtherAmount(unzipMap.get(EnumUnZipFieldNameV9.QTFY.getCode()));
        responseDtoBuilder.sort(bean.getFeeSortid());
        // 含税总计 TaxGCF = GCF + ZZSHJ + qf_ZZSHJ
        responseDtoBuilder.feeState(bean.getFeeState());
        return responseDtoBuilder.build();
    }
    @IterableMapping(qualifiedByName = "convert3")
    List<EngineeringFeesRespVO> convertList3(List<PrjProjectCostDTO> bean);

    /**
     * 项目总概算-工程费汇总转换
     */
    default EngineeringFeesRespVO convert4(PrjEngineeringDTO bean){
        Map<String, BigDecimal> unzipMap = V9BinaryToMapUtil.unpackData(bean.getEngParameters(), (BigDecimal value) -> value.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));
        EngineeringFeesRespVO.EngineeringFeesRespVOBuilder responseDtoBuilder = EngineeringFeesRespVO.builder();
        responseDtoBuilder.name(bean.getEngName());
        responseDtoBuilder.feeCode(bean.getEngCode());
        BigDecimal sbgzf = unZipDataToDto(EnumUnZipFieldName.SBGZF, unzipMap);
        BigDecimal jksbgzf = unZipDataToDto(EnumUnZipFieldName.JKSBGZF, unzipMap);
        responseDtoBuilder.emAmount(sbgzf.add(jksbgzf));
        BigDecimal zyclf = unZipDataToDto(EnumUnZipFieldName.ZCGZF, unzipMap);
        BigDecimal jkzyclf = unZipDataToDto(EnumUnZipFieldName.JKZCGZF, unzipMap);
        responseDtoBuilder.mainMaterialAmount(zyclf.add(jkzyclf));

        BigDecimal azf = unzipMap.get(EnumUnZipFieldNameV9.AZ_ZHF.getCode());
        BigDecimal jzf = unzipMap.get(EnumUnZipFieldNameV9.JZ_ZHF.getCode());

        responseDtoBuilder.installAmount(azf);
        responseDtoBuilder.projectBuildAmount(jzf);

        // GCF = SBGZF + JKSBGZF + ZCGZF + JKZCGZF + (AZF+qf_AZAQSCF) + JZGCF+qf_JZAQSCF
        BigDecimal gcf = NumberUtil.add(sbgzf, jksbgzf, zyclf, jkzyclf, azf, jzf);
        responseDtoBuilder.projectTotalAmount(gcf);
        responseDtoBuilder.projectForeignAmount(unZipDataToDto(EnumUnZipFieldName.WBF, unzipMap));

        // ZZSHJ = ZCZZS + JKZCZZS + SBZZS + JKSBZZS + JZ_SGZZS + qf_JZ_SGZZS + AZ_SGZZS + qf_AZ_SGZZS
        BigDecimal ZZSHJ = unZipDataToDto(EnumUnZipFieldName.ZCZZS, unzipMap)
                .add(unZipDataToDto(EnumUnZipFieldName.JKZCZZS, unzipMap)).add(unZipDataToDto(EnumUnZipFieldName.SBZZS, unzipMap))
                .add(unZipDataToDto(EnumUnZipFieldName.JKSBZZS, unzipMap)).add(unZipDataToDto(EnumUnZipFieldName.JZ_SGZZS, unzipMap))
                .add(unZipDataToDto(EnumUnZipFieldName.qf_JZ_SGZZS, unzipMap)).add(unZipDataToDto(EnumUnZipFieldName.AZ_SGZZS, unzipMap))
                .add(unZipDataToDto(EnumUnZipFieldName.qf_AZ_SGZZS, unzipMap));
        responseDtoBuilder.vat(ZZSHJ);

        // 含税总计 TaxGCF = 合计+增值税
        BigDecimal taxGCF = gcf.add(ZZSHJ);

        responseDtoBuilder.vatTotal(taxGCF);
        return responseDtoBuilder.build();
    }

    /**
     * 项目总概算-其他费部分转换
     * @param bean
     * @return
     */
    @Named("convert5")
    default EngineeringFeesRespVO convert5(PrjProjectCostDTO bean){
        Map<String, BigDecimal> unzipMap = V9BinaryToMapUtil.unpackData(bean.getFeeParameters(), (BigDecimal value) -> value.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));
        EngineeringFeesRespVO.EngineeringFeesRespVOBuilder responseDtoBuilder = EngineeringFeesRespVO.builder();
        responseDtoBuilder.id(bean.getFeeId());
        responseDtoBuilder.pid(bean.getFeePid());
        responseDtoBuilder.feeCode(bean.getFeeCode());
        responseDtoBuilder.indexNo(bean.getFeeSequence());
        responseDtoBuilder.name(bean.getFeeName());
        responseDtoBuilder.emAmount(unZipDataToDto(EnumUnZipFieldName.COST_SBF, unzipMap));
        responseDtoBuilder.mainMaterialAmount(unZipDataToDto(EnumUnZipFieldName.COST_ZCF, unzipMap));
        responseDtoBuilder.installAmount(unZipDataToDto(EnumUnZipFieldName.COST_AZF, unzipMap));
        responseDtoBuilder.projectBuildAmount(unZipDataToDto(EnumUnZipFieldName.COST_JZF, unzipMap));
        responseDtoBuilder.projectTotalAmount(unZipDataToDto(EnumUnZipFieldName.GCF, unzipMap));
        if (!"F000".equals(bean.getFeeCode()) || !"工程建设总概算".equals(bean.getFeeName())) {
            responseDtoBuilder.vat(unZipDataToDto(EnumUnZipFieldName.ZZSHJ, unzipMap));
            BigDecimal taxGCF = unZipDataToDto(EnumUnZipFieldName.GCF, unzipMap).add(unZipDataToDto(EnumUnZipFieldName.ZZSHJ, unzipMap)).add(unZipDataToDto(EnumUnZipFieldName.qf_ZZSHJ, unzipMap));
            responseDtoBuilder.vatTotal(taxGCF);
        }else {
            responseDtoBuilder.vatTotal(unZipDataToDto(EnumUnZipFieldName.GCF, unzipMap));
        }
        responseDtoBuilder.projectForeignAmount(NumberUtil.add(unZipDataToDto(EnumUnZipFieldName.WBF, unzipMap), unZipDataToDto(EnumUnZipFieldName.WBSBF, unzipMap), unZipDataToDto(EnumUnZipFieldName.WBZCF, unzipMap)));
        BigDecimal qtfyV9 = unzipMap.get(EnumUnZipFieldNameV9.QTFY.getCode());
        responseDtoBuilder.projectOtherAmount(qtfyV9 != null ? qtfyV9 : unZipDataToDto(EnumUnZipFieldName.COST_QTF, unzipMap));
        responseDtoBuilder.sort(bean.getFeeSortid());
        // 含税总计 TaxGCF = GCF + ZZSHJ + qf_ZZSHJ
        responseDtoBuilder.feeState(bean.getFeeState());
        return responseDtoBuilder.build();
    }
    @IterableMapping(qualifiedByName = "convert5")
    List<EngineeringFeesRespVO> convert5(List<PrjProjectCostDTO> bean);


    /**
     * 项目总概算-分部数据转换
     * @param bean
     * @return
     */
    default EngineeringFeesRespVO convert6(PrjSectionBillDTO bean){
        Map<String, BigDecimal> unzipMap = V9BinaryToMapUtil.unpackData(bean.getBillParameters(), (BigDecimal value) -> value.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));
        EngineeringFeesRespVO.EngineeringFeesRespVOBuilder responseDtoBuilder = EngineeringFeesRespVO.builder();
        responseDtoBuilder.name(bean.getBillName());
        responseDtoBuilder.engClass(ENGTypeEnum.FENBU.getCode());
        responseDtoBuilder.designScale(bean.getBillExpression());
        BigDecimal sbgzf = unZipDataToDto(EnumUnZipFieldName.SBGZF, unzipMap);
        BigDecimal jksbgzf = unZipDataToDto(EnumUnZipFieldName.JKSBGZF, unzipMap);
        responseDtoBuilder.emAmount(sbgzf.add(jksbgzf));
        BigDecimal zyclf = unZipDataToDto(EnumUnZipFieldName.ZCGZF, unzipMap);
        BigDecimal jkzyclf = unZipDataToDto(EnumUnZipFieldName.JKZCGZF, unzipMap);
        responseDtoBuilder.mainMaterialAmount(zyclf.add(jkzyclf));

        BigDecimal azf = BigDecimal.ZERO;
        BigDecimal jzf = BigDecimal.ZERO;
        if (BillProfessionEnum.INSTALL.getCode().equals(bean.getBillProfessionalcode())) {
            //安全费+安装 安全生产费
            azf = unZipDataToDto(EnumUnZipFieldName.AZF, unzipMap).add(unZipDataToDto(EnumUnZipFieldName.qf_AZAQSCF, unzipMap));
            responseDtoBuilder.installAmount(azf);
        }
        if (BillProfessionEnum.BUILD.getCode().equals(bean.getBillProfessionalcode())) {
            //安全费+建筑 安全生产费
            jzf = unZipDataToDto(EnumUnZipFieldName.JZGCF, unzipMap).add(unZipDataToDto(EnumUnZipFieldName.qf_JZAQSCF, unzipMap));
            responseDtoBuilder.projectBuildAmount(jzf);
        }

        // GCF = SBGZF + JKSBGZF + ZCGZF + JKZCGZF + (AZF+qf_AZAQSCF) + JZGCF+qf_JZAQSCF
        BigDecimal gcf = sbgzf.add(jksbgzf).add(zyclf).add(jkzyclf).add(azf).add(jzf);
        responseDtoBuilder.projectTotalAmount(gcf);
        responseDtoBuilder.projectForeignAmount(unZipDataToDto(EnumUnZipFieldName.WBF, unzipMap));

        BigDecimal ZZSHJ = unZipDataToDto(EnumUnZipFieldName.ZCZZS, unzipMap)
                .add(unZipDataToDto(EnumUnZipFieldName.JKZCZZS, unzipMap)).add(unZipDataToDto(EnumUnZipFieldName.SBZZS, unzipMap))
                .add(unZipDataToDto(EnumUnZipFieldName.JKSBZZS, unzipMap)).add(unZipDataToDto(EnumUnZipFieldName.JZ_SGZZS, unzipMap))
                .add(unZipDataToDto(EnumUnZipFieldName.qf_JZ_SGZZS, unzipMap)).add(unZipDataToDto(EnumUnZipFieldName.AZ_SGZZS, unzipMap))
                .add(unZipDataToDto(EnumUnZipFieldName.qf_AZ_SGZZS, unzipMap));
        // 兼容一下v9分部增值税
        BigDecimal zzsf = unzipMap.get(EnumUnZipFieldNameV9.ZZSF.getCode());
        responseDtoBuilder.vat(zzsf == null ? ZZSHJ : zzsf);
        // 含税总计 TaxGCF = 合计+增值税
        responseDtoBuilder.vatTotal(gcf.add(zzsf == null ? ZZSHJ : zzsf));

        return responseDtoBuilder.build();
    }
    List<EngineeringFeesRespVO> convert6(List<PrjSectionBillDTO> bean);
    default ProjectWbsChildRespVO convert7(PrjEngineeringDTO engDTO){
        if (engDTO == null) {
            return null;
        }
        ProjectWbsChildRespVO.ProjectWbsChildRespVOBuilder builder = ProjectWbsChildRespVO.builder();
        builder.id(engDTO.getEngId());
        builder.childType(ChildTypeEnum.SUBSECTION.getCode());
        builder.structureId(engDTO.getEngId());
        builder.structurePid(engDTO.getEngPid());
        builder.code(engDTO.getEngCode());
        builder.name(engDTO.getEngName());

        builder.priceParam(V9BinaryToMapUtil.unpackData(engDTO.getEngParameters(), (BigDecimal value) -> value));
        return builder.build();
    }


    /**
     * 子目结构-分部数据转换
     */
    default ProjectWbsChildRespVO convert8(PrjSectionBillDTO bean) {
        if (bean == null) {
            return null;
        }
        ProjectWbsChildRespVO.ProjectWbsChildRespVOBuilder builder = ProjectWbsChildRespVO.builder();
        builder.id(bean.getBillId());
        builder.childType(ChildTypeEnum.SUBSECTION.getCode());
        builder.structureId(bean.getBillId());
        builder.structurePid(bean.getBillPid());
        builder.code(bean.getBillCode());
        builder.name(bean.getBillName());
        builder.typeName("分部");
        builder.unit(bean.getBillUnit());

        builder.priceParam(V9BinaryToMapUtil.unpackData(bean.getBillParameters(), (BigDecimal value) -> value));
        return builder.build();
    }

    List<ProjectWbsChildRespVO> convertList8(List<PrjSectionBillDTO> bean);

    /**
     * 子目数据转换
     * @param bean
     * @return
     */
    default ProjectWbsChildRespVO convert9(PrjNormDTO bean){
        if (bean == null) {
            return null;
        }
        ProjectWbsChildRespVO.ProjectWbsChildRespVOBuilder builder = ProjectWbsChildRespVO.builder();
        builder.id(bean.getNormId());
        builder.childType(ChildTypeEnum.SUBITEM.getCode());
        builder.structureId(bean.getNormId());
        builder.structurePid(bean.getNormPid());

        builder.code(bean.getNormCode());
        builder.name(bean.getNormName() + (StringUtils.isNotBlank(bean.getNormStandard()) ? ("<br>" + bean.getNormStandard()) : ""));
        builder.typeName("子目");
        builder.unit(bean.getNormUnit());
        builder.material(bean.getNormCompositions());

        builder.projectCount(Convert.toBigDecimal(bean.getNormAmount(), BigDecimal.ZERO).setScale(3, BigDecimal.ROUND_HALF_UP).toString());
        builder.projectCountExpression(bean.getNormExpression());
        builder.singleWeight(bean.getNormWeight());

        builder.priceParam(V9BinaryToMapUtil.unpackData(bean.getNormParameters(), (BigDecimal value) -> value));
        return builder.build();
    }
    List<ProjectWbsChildRespVO> convertList9(List<PrjNormDTO> bean);

    /**
     * 其他费数据转换
     */
    default OtherFeesRespVO convert10(PrjProjectCostDTO bean) {
        if (bean == null) {
            return null;
        }
        Map<String, BigDecimal> unzipMap = V9BinaryToMapUtil.unpackData(bean.getFeeParameters(), (BigDecimal value) -> value);
        OtherFeesRespVO.OtherFeesRespVOBuilder builder = OtherFeesRespVO.builder();
        builder.id(bean.getFeeId());
        builder.pid(bean.getFeePid());
        builder.indexNo(bean.getFeeSequence());
        builder.name(bean.getFeeName());
        builder.sort(bean.getFeeSortid());
        builder.calculationFormula(bean.getFeeExpression());
        builder.calculationBasis(bean.getFeeExpressionbasis());
        builder.projectOtherAmount(unZipDataToDto(EnumUnZipFieldNameV9.QTFY, unzipMap));
        BigDecimal wbf = unZipDataToDto(EnumUnZipFieldNameV9.WBF, unzipMap);
        BigDecimal wbzcf = unZipDataToDto(EnumUnZipFieldNameV9.WBZCF, unzipMap);
        BigDecimal wbsbf = unZipDataToDto(EnumUnZipFieldNameV9.WBSBF, unzipMap);
        builder.projectForeignAmount(NumberUtil.add(wbf, wbzcf, wbsbf));
        builder.vatRate(String.valueOf(NumberUtil.mul(bean.getFeeTaxrate(), 100).setScale(0, RoundingMode.HALF_UP)));
        builder.vat(unZipDataToDto(EnumUnZipFieldName.QTFZZS, unzipMap));
        builder.rate(NumberUtil.mul(bean.getFeeRate(), 100).setScale(2, RoundingMode.HALF_UP));
        builder.instruction(bean.getFeeDescription());
        builder.feeState(bean.getFeeState());
        return builder.build();
    }
    List<OtherFeesRespVO> convertList10(List<PrjProjectCostDTO> bean);

}
