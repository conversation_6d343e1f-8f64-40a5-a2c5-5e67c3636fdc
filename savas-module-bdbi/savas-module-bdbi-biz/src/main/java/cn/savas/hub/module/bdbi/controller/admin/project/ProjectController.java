package cn.savas.hub.module.bdbi.controller.admin.project;

import cn.savas.hub.framework.common.pojo.CommonResult;
import cn.savas.hub.framework.common.util.object.BeanUtils;
import cn.savas.hub.module.bdbi.controller.admin.project.vo.NormCommentReqVO;
import cn.savas.hub.module.bdbi.controller.admin.project.vo.NormCommentRespVO;
import cn.savas.hub.module.bdbi.service.project.NormService;
import cn.savas.hub.module.bdbi.service.project.ProjectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import static cn.savas.hub.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @date 2024/11/26 09:52
 */
@Tag(name = "协同前端 - 项目功能")
@RestController
@RequestMapping("/bdbi/project")
public class ProjectController {
    @Resource
    private NormService normService;
    @Resource
    private ProjectService projectService;

    @Operation(summary = "添加子目批注")
    @PostMapping("/addComment")
    public CommonResult<Boolean> getMajorWbsTree(@Valid @RequestBody NormCommentReqVO reqVO) {
        normService.addComment(reqVO);
        return success(true);
    }

    @Operation(summary = "获取子目批注")
    @GetMapping("/getComment")
    public CommonResult<Map<String, String>> getComment(@RequestParam("projectId") Long projectId,
                                                        @RequestParam("wbsId") Long wbsId,
                                                        @RequestParam("tableDataId") Long tableDataId) {
        List<NormCommentRespVO> comment = BeanUtils.toBean(normService.getComments(projectId, wbsId, tableDataId), NormCommentRespVO.class);
        if (comment.size() == 1) {
            return success(comment.get(0).getComment());
        }
        return success(null);
    }

    @Operation(summary = "项目流程进度")
    @GetMapping("/getProjectFlow")
    public CommonResult<Map<String, Object>> getProjectFlow(@RequestParam("projectId") Long projectId) {
        return success(projectService.getProjectFlow(projectId));
    }
}
