package cn.savas.hub.module.bdbi.controller.admin.project.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/20 16:37
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EngineeringFeesRespVO {
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 上级id
     */
    @Schema(description = "上级id")
    private Long pid;

    /**
     *
     */
    private String feeCode;

    /**
     * 序号
     */
    @Schema(description = "序号")
    private String indexNo;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;


    /**
     * 设备购置费
     */
    @Schema(description = "设备购置费")
    private BigDecimal emAmount;

    /**
     * 主要材料费
     */
    @Schema(description = "主要材料费")
    private BigDecimal mainMaterialAmount;

    /**
     * 安装费
     */
    @Schema(description = "安装费")
    private BigDecimal installAmount;

    /**
     * 建筑工程费
     */
    @Schema(description = "建筑工程费")
    private BigDecimal projectBuildAmount;

    /**
     * 其他费
     */
    @Schema(description = "其他费")
    private BigDecimal projectOtherAmount;

    /**
     * 合计
     */
    @Schema(description = "合计")
    private BigDecimal projectTotalAmount;

    /**
     * 增值税
     */
    @Schema(description = "增值税")
    private BigDecimal vat;

    /**
     * 含税总计
     */
    @Schema(description = "含税总计")
    private BigDecimal vatTotal;

    /**
     * 投资占比
     */
    @Schema(description = "投资占比")
    private String projectInvestmentProporttion;

    /**
     * 外币金额
     */
    @Schema(description = "外币金额")
    private BigDecimal projectForeignAmount;

    @Schema(description = "工程费类型")
    private Integer engClass;

    private List<EngineeringFeesRespVO> children;

    /**
     * 设计规模主要工程量
     */
    @Schema(description = "设计规模主要工程量")
    private String designScale;

    private Integer sort;

    /**
     * 费用状态
     */
    @Schema(description = "费用状态")
    private Long feeState;
}
