package cn.savas.hub.module.bdbi.service.project;

import cn.savas.hub.framework.common.util.collection.TreeDataUtil;
import cn.savas.hub.module.bdbi.controller.admin.project.vo.FlowColumnRespVO;
import cn.savas.hub.module.bpm.api.client.BpmModelApi;
import cn.savas.hub.module.bpm.api.client.dto.BpmnModelNodeRespDTO;
import cn.savas.hub.module.bpm.enums.client.ClientWorkStateEnum;
import cn.savas.hub.module.bpm.enums.task.BpmTaskStatusEnum;
import cn.savas.hub.module.client.api.project.ClientProjectFileApi;
import cn.savas.hub.module.collect.api.CollectDataApi;
import cn.savas.hub.module.collect.api.dto.WbsTreeDTO;
import cn.savas.hub.module.system.api.user.AdminUserApi;
import cn.savas.hub.module.system.api.user.dto.AdminUserRespDTO;
import com.google.common.collect.ImmutableMap;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/26 09:53
 */
@Service
public class ProjectServiceImpl implements ProjectService {
    @Resource
    private ClientProjectFileApi clientProjectFileApi;
    @Resource
    private CollectDataApi collectDataApi;
    @Resource
    private BpmModelApi bpmModelApi;
    @Resource
    private AdminUserApi adminUserApi;

    @Override
    public Map<String, Object> getProjectFlow(Long projectId) {
        // 获取流程模型信息
        List<BpmnModelNodeRespDTO> nodeRespList = bpmModelApi.getFlowNodeTemplate(projectId);
        // 获取列数据
        List<FlowColumnRespVO> column = getFlowColumnData(nodeRespList);
        // 获取行数据
        List<Map<String, Object>> rows = getFlowRowData(projectId, nodeRespList);
        return ImmutableMap.of("column", column, "rows", rows);
    }
    /**
     * 处理列数据
     */
    private List<FlowColumnRespVO> getFlowColumnData(List<BpmnModelNodeRespDTO> nodeRespList) {
        List<FlowColumnRespVO> column = new ArrayList<>();
        column.add(new FlowColumnRespVO("projectName", "项目名称"));
        column.add(new FlowColumnRespVO("creator", "项目创建者"));
        // 判断子节点
        Set<Long> flowNodePidSet = nodeRespList.stream().map(BpmnModelNodeRespDTO::getParentid).collect(Collectors.toSet());
        // 设置节点列
        nodeRespList.stream()
                .filter(e->!flowNodePidSet.contains(e.getId())) // 判断子节点
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                BpmnModelNodeRespDTO::getNodeArchiveId, // 用于去重的字段
                                item -> item,
                                (existing, replacement) -> existing, // 如果有重复，保留第一个元素
                                LinkedHashMap::new
                        ),
                        map -> new ArrayList<>(map.values())
                )).forEach(node -> column.add(new FlowColumnRespVO(String.valueOf(node.getId()), node.getNodeName())));
        return column;
    }
    /**
     * 获取行数据
     */
    private List<Map<String, Object>> getFlowRowData(Long projectId, List<BpmnModelNodeRespDTO> nodeRespList) {
        // 获取相关用户信息
        Set<Long> userIdSet = nodeRespList.stream().map(BpmnModelNodeRespDTO::getNodeUserId).collect(Collectors.toSet());
        Map<Long, AdminUserRespDTO> userMap = adminUserApi.getUserMap(userIdSet);
        // 获取全部的工程节点
        List<WbsTreeDTO> wbsTree = clientProjectFileApi.changePrjFileSumDs(projectId, () -> collectDataApi.getWbsTree(projectId));
        wbsTree = TreeDataUtil.baseTreeV2(wbsTree, true, WbsTreeDTO::getId, WbsTreeDTO::getPid, WbsTreeDTO::setChildren, WbsTreeDTO::getChildren);
        Map<Long, List<BpmnModelNodeRespDTO>> hostmodelIdMap = nodeRespList.stream().collect(Collectors.groupingBy(BpmnModelNodeRespDTO::getNodeHostmodel));
        return setRowsData(wbsTree, hostmodelIdMap, userMap);
    }

    /**
     * 设置行数据
     * @param wbsTree 工程结构
     * @param nodeRespList 流程模型
     */
    private List<Map<String, Object>> setRowsData(List<WbsTreeDTO> wbsTree,
                                                  Map<Long, List<BpmnModelNodeRespDTO>> hostmodelIdMap,
                                                  Map<Long, AdminUserRespDTO> userMap) {
        List<Map<String, Object>> rows = new ArrayList<>();
        for (WbsTreeDTO treeDTO : wbsTree) {
            Map<String, Object> row = createRow(treeDTO, hostmodelIdMap.getOrDefault(treeDTO.getId(), new ArrayList<>()), userMap);
            // 递归处理子节点
            if (!CollectionUtils.isEmpty(treeDTO.getChildren())) {
                row.put("children", setRowsData(treeDTO.getChildren(), hostmodelIdMap, userMap));
            }
            rows.add(row);
        }
        return rows;
    }

    /**
     * 创建行数据
     * @param wbsTreeDTO 工程结构
     * @param nodeList 流程节点
     * @param userMap 用户信息
     */
    private Map<String, Object> createRow(WbsTreeDTO wbsTreeDTO,
                                          List<BpmnModelNodeRespDTO> nodeList,
                                          Map<Long, AdminUserRespDTO> userMap
    ) {
        Map<String, Object> row = new LinkedHashMap<>();
        row.put("projectName", wbsTreeDTO.getName());
        row.put("id", wbsTreeDTO.getId());
        row.put("creator", wbsTreeDTO.getUserName());
        for (BpmnModelNodeRespDTO nodeDTO : nodeList) {
            Map<String, Object> fieldsMap = new HashMap<>();
            // 节点名称、状态
            AdminUserRespDTO user = userMap.get(nodeDTO.getNodeUserId());
            if (user != null) {
                fieldsMap.put("name", user.getNickname());
            }
            fieldsMap.put("status", (nodeDTO.getNodeState() & 64) == ClientWorkStateEnum.RUNNING.getState()? BpmTaskStatusEnum.RUNNING.getStatus():BpmTaskStatusEnum.APPROVE.getStatus());
            row.put(String.valueOf(nodeDTO.getId()), fieldsMap);
        }
        return row;
    }
}
