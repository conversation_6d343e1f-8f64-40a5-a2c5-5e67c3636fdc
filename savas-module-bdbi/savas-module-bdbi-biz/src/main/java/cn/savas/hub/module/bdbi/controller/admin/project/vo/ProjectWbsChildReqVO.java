package cn.savas.hub.module.bdbi.controller.admin.project.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/22 15:04
 */
@Data
public class ProjectWbsChildReqVO {

    @Schema(description ="wbsId")
    private Long wbsId;

    @Schema(description ="wbs类型")
    private Long wbsType;

    @Schema(description ="专业归类 1：建筑，3：安装，7：进口")
    private Integer specialtyType;

    @Schema(description ="项目id")
    private Long projectId;

    @Schema(description ="CBS分部名称")
    private String fbName;

}
