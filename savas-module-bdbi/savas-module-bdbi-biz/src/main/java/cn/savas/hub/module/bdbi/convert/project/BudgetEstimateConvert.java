package cn.savas.hub.module.bdbi.convert.project;

import cn.hutool.core.util.NumberUtil;
import cn.savas.hub.framework.common.client.enmus.EnumUnZipFieldNameV9;
import cn.savas.hub.framework.common.client.util.V9BinaryToMapUtil;
import cn.savas.hub.module.bdbi.api.budgetestimate.dto.NormSearchRespDTO;
import cn.savas.hub.module.bdbi.controller.admin.project.vo.NormSearchReqVO;
import cn.savas.hub.module.bdbi.controller.admin.project.vo.OtherEngFeeRespVO;
import cn.savas.hub.module.bdbi.controller.admin.project.vo.OtherFeeRespVO;
import cn.savas.hub.module.bdbi.controller.admin.project.vo.TotalEstimateRespVO;
import cn.savas.hub.module.bdbi.enums.budget.ChildTypeEnum;
import cn.savas.hub.module.collect.api.dto.*;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/12 11:27
 */
@Mapper(imports = {ChildTypeEnum.class, V9BinaryToMapUtil.class, StringUtils.class})
public interface BudgetEstimateConvert {
    BudgetEstimateConvert INSTANCE = org.mapstruct.factory.Mappers.getMapper(BudgetEstimateConvert.class);

    default OtherEngFeeRespVO convert1(OtherEngFeeDTO bean){
        if ( bean == null ) {
            return null;
        }

        OtherEngFeeRespVO otherEngFeeRespVO = new OtherEngFeeRespVO();

        otherEngFeeRespVO.setId( bean.getFeeId() );
        otherEngFeeRespVO.setPid( bean.getFeePid() );
        otherEngFeeRespVO.setFeeHostmodel( bean.getFeeHostmodel() );
        otherEngFeeRespVO.setFeeSequence( bean.getFeeSequence() );
        otherEngFeeRespVO.setFeeName( bean.getFeeName() );
        otherEngFeeRespVO.setFeeCode( bean.getFeeCode() );
        otherEngFeeRespVO.setFeeExpense( bean.getFeeExpense() );
        otherEngFeeRespVO.setFeeExpression( bean.getFeeExpression() );
        otherEngFeeRespVO.setFeeRate( bean.getFeeRate() );
        otherEngFeeRespVO.setFeeValue( bean.getFeeValue() );
        otherEngFeeRespVO.setFeeTaxrate( bean.getFeeTaxrate() );
        otherEngFeeRespVO.setFeeTax(NumberUtil.mul(bean.getFeeValue(), bean.getFeeTaxrate()));
        return otherEngFeeRespVO;
    }
    List<OtherEngFeeRespVO> convertList1(List<OtherEngFeeDTO> bean);

    default OtherFeeRespVO convert2(OtherFeeDTO bean){
        if ( bean == null ) {
            return null;
        }
        Map<String, BigDecimal> parameterMap = V9BinaryToMapUtil.unpackData(bean.getFeeParameters());

        OtherFeeRespVO otherFeeRespVO = new OtherFeeRespVO();

        otherFeeRespVO.setId( bean.getFeeId() );
        otherFeeRespVO.setPid( bean.getFeePid() );
        otherFeeRespVO.setFeeHostmodel( bean.getFeeHostmodel() );
        otherFeeRespVO.setFeeSequence( bean.getFeeSequence() );
        otherFeeRespVO.setFeeName( bean.getFeeName() );
        otherFeeRespVO.setFeeCode( bean.getFeeCode() );
        otherFeeRespVO.setFeeExpression( bean.getFeeExpression() );
        otherFeeRespVO.setFeeRate( bean.getFeeRate());
        otherFeeRespVO.setFeeValue( bean.getFeeValue());
        otherFeeRespVO.setFeeForeignValue(parameterMap.get(EnumUnZipFieldNameV9.WBF.getCode()));
        otherFeeRespVO.setFeeTaxrate( bean.getFeeTaxrate());
        otherFeeRespVO.setFeeTax(NumberUtil.mul(bean.getFeeValue(), bean.getFeeTaxrate()));
        otherFeeRespVO.setFeeExpense( bean.getFeeExpense() );
        otherFeeRespVO.setFeeCalculatorbasis( bean.getFeeCalculatorbasis() );

        return otherFeeRespVO;
    }

    List<OtherFeeRespVO> convertList2(List<OtherFeeDTO> bean);


    default TotalEstimateRespVO convert3(OtherFeeDTO bean){
        if ( bean == null ) {
            return null;
        }
        Map<String, BigDecimal> unzipMap = V9BinaryToMapUtil.unpackData(bean.getFeeParameters(), (BigDecimal value) -> value.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));

        TotalEstimateRespVO totalEstimateRespVO = new TotalEstimateRespVO();

        totalEstimateRespVO.setId( bean.getFeeId() );
        totalEstimateRespVO.setPid( bean.getFeePid() );
        totalEstimateRespVO.setFeeHostmodel( bean.getFeeHostmodel() );
        totalEstimateRespVO.setFeeSequence( bean.getFeeSequence() );
        totalEstimateRespVO.setFeeCode( bean.getFeeCode() );
        totalEstimateRespVO.setFeeName( bean.getFeeName() );

        BigDecimal sbgzf = unzipMap.get(EnumUnZipFieldNameV9.SBGZF.getCode());
        BigDecimal jksbgzf = unzipMap.get(EnumUnZipFieldNameV9.JK_SBGZF.getCode());
        totalEstimateRespVO.setEmAmount(NumberUtil.add(sbgzf, jksbgzf));

        BigDecimal zyclf = unzipMap.get(EnumUnZipFieldNameV9.ZYCLF.getCode());
        BigDecimal jkzyclf = unzipMap.get(EnumUnZipFieldNameV9.JKZYCLF.getCode());
        totalEstimateRespVO.setMainMaterialAmount(NumberUtil.add(zyclf, jkzyclf));

        totalEstimateRespVO.setInstallAmount(unzipMap.get(EnumUnZipFieldNameV9.AZ_ZHF.getCode()));
        totalEstimateRespVO.setProjectBuildAmount(unzipMap.get(EnumUnZipFieldNameV9.JZ_ZHF.getCode()));
        totalEstimateRespVO.setProjectOtherAmount(unzipMap.get(EnumUnZipFieldNameV9.QTFY.getCode()));
        BigDecimal totalAmount = unzipMap.get(EnumUnZipFieldNameV9.FEE.getCode());
        totalEstimateRespVO.setProjectTotalAmount(totalAmount);
        BigDecimal zzs = unzipMap.get(EnumUnZipFieldNameV9.ZZSF.getCode());
        totalEstimateRespVO.setVat(zzs);
        totalEstimateRespVO.setVatTotal(NumberUtil.add(totalAmount, zzs));

        BigDecimal wbf = unzipMap.get(EnumUnZipFieldNameV9.WBF.getCode());
        BigDecimal wbzcf = unzipMap.get(EnumUnZipFieldNameV9.WBZCF.getCode());
        BigDecimal wbsbf = unzipMap.get(EnumUnZipFieldNameV9.WBSBF.getCode());
        totalEstimateRespVO.setProjectForeignAmount(NumberUtil.add(wbf, wbzcf, wbsbf));
        return totalEstimateRespVO;
    }
    List<TotalEstimateRespVO> convertList3(List<OtherFeeDTO> bean);



    default TotalEstimateRespVO convert4(PrjEngineeringDTO bean){
        if ( bean == null ) {
            return null;
        }

        TotalEstimateRespVO totalEstimateRespVO = new TotalEstimateRespVO();

        totalEstimateRespVO.setId( bean.getEngId() );
        totalEstimateRespVO.setPid( bean.getEngPid() );
        totalEstimateRespVO.setFeeCode( bean.getEngCode() );
        totalEstimateRespVO.setFeeName( bean.getEngName() );

        Map<String, BigDecimal> unzipMap = V9BinaryToMapUtil.unpackData(bean.getEngParameters(), (BigDecimal value) -> value.divide(BigDecimal.valueOf(10000), 2, BigDecimal.ROUND_HALF_UP));

        BigDecimal sbgzf = unzipMap.get(EnumUnZipFieldNameV9.SBGZF.getCode());
        BigDecimal jksbgzf = unzipMap.get(EnumUnZipFieldNameV9.JK_SBGZF.getCode());
        totalEstimateRespVO.setEmAmount(NumberUtil.add(sbgzf, jksbgzf));

        BigDecimal zyclf = unzipMap.get(EnumUnZipFieldNameV9.ZYCLF.getCode());
        BigDecimal jkzyclf = unzipMap.get(EnumUnZipFieldNameV9.JKZYCLF.getCode());
        totalEstimateRespVO.setMainMaterialAmount(NumberUtil.add(zyclf, jkzyclf));

        totalEstimateRespVO.setInstallAmount(unzipMap.get(EnumUnZipFieldNameV9.AZ_ZHF.getCode()));
        totalEstimateRespVO.setProjectBuildAmount(unzipMap.get(EnumUnZipFieldNameV9.JZ_ZHF.getCode()));
        totalEstimateRespVO.setProjectOtherAmount(unzipMap.get(EnumUnZipFieldNameV9.QTFY.getCode()));
        BigDecimal totalAmount = unzipMap.get(EnumUnZipFieldNameV9.FEE.getCode());
        totalEstimateRespVO.setProjectTotalAmount(totalAmount);
        BigDecimal zzs = unzipMap.get(EnumUnZipFieldNameV9.ZZSF.getCode());
        totalEstimateRespVO.setVat(zzs);
        totalEstimateRespVO.setVatTotal(NumberUtil.add(totalAmount, zzs));
        totalEstimateRespVO.setEngExpression( bean.getEngExpression() );

        BigDecimal wbf = unzipMap.get(EnumUnZipFieldNameV9.WBF.getCode());
        BigDecimal wbzcf = unzipMap.get(EnumUnZipFieldNameV9.WBZCF.getCode());
        BigDecimal wbsbf = unzipMap.get(EnumUnZipFieldNameV9.WBSBF.getCode());
        totalEstimateRespVO.setProjectForeignAmount(NumberUtil.add(wbf, wbzcf, wbsbf));


        return totalEstimateRespVO;
    }
    List<TotalEstimateRespVO> convertList4(List<PrjEngineeringDTO> bean);

    @Mappings({
             @Mapping(target = "name", source = "normLike"),
             @Mapping(target = "unit", source = "dwName"),
             @Mapping(target = "compositions", source = "czName"),

    })
    PrjNormQueryDTO convert5(NormSearchReqVO reqVO);

    @Mappings({
            @Mapping(target = "id", source = "engId"),
            @Mapping(target = "engId", source = "engId"),
            @Mapping(target = "normType", expression = "java(ChildTypeEnum.SUBSECTION.getCode())"),
            @Mapping(target = "structureId", source = "engId"),
            @Mapping(target = "structurePid", source = "engPid"),
            @Mapping(target = "code", source = "engCode"),
            @Mapping(target = "name", source = "engName"),
            @Mapping(target = "priceParam", expression = "java(V9BinaryToMapUtil.unpackData(bean.getEngParameters()))"),
    })
    NormSearchRespDTO convert6(PrjEngineeringDTO bean);


    @Mappings({
            @Mapping(target = "id", source = "billId"),
            @Mapping(target = "engId", source = "billHostmodel"),
            @Mapping(target = "normType", expression = "java(ChildTypeEnum.SUBSECTION.getCode())"),
            @Mapping(target = "structureId", source = "billId"),
            @Mapping(target = "structurePid", source = "billPid"),
            @Mapping(target = "code", source = "billCode"),
            @Mapping(target = "name", source = "billName"),
            @Mapping(target = "normTypeName", constant = "分部"),
            @Mapping(target = "unit", source = "billUnit"),
            @Mapping(target = "priceParam", expression = "java(V9BinaryToMapUtil.unpackData(bean.getBillParameters()))"),
    })
    NormSearchRespDTO convert7(PrjSectionBillDTO bean);
    List<NormSearchRespDTO> convertList7(List<PrjSectionBillDTO> bean);

    @Mappings({
            @Mapping(target = "id", source = "normId"),
            @Mapping(target = "engId", source = "normHostmodel"),
            @Mapping(target = "normType", expression = "java(ChildTypeEnum.SUBITEM.getCode())"),
            @Mapping(target = "structureId", source = "normId"),
            @Mapping(target = "structurePid", source = "normPid"),
            @Mapping(target = "code", source = "normCode"),
            @Mapping(target = "name", expression = "java(bean.getNormName() + (StringUtils.isNotBlank(bean.getNormStandard()) ? (\"<br>\" + bean.getNormStandard()) : \"\"))"),
            @Mapping(target = "normTypeName", constant = "子目"),
            @Mapping(target = "unit", source = "normUnit"),
            @Mapping(target = "priceParam", expression = "java(V9BinaryToMapUtil.unpackData(bean.getNormParameters()))"),
    })
    NormSearchRespDTO convert8(PrjNormDTO bean);
    List<NormSearchRespDTO> convertList8(List<PrjNormDTO> normList);
}
