package cn.savas.hub.module.bdbi.service.device.decorator;

import cn.savas.hub.module.bdbi.api.device.dto.DeviceComparisonResponseDto;
import cn.savas.hub.module.bdbi.api.device.dto.DeviceComparisonTableHeaderDto;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description: 表数据
 * @author: zq
 * @date: 2022/4/25 11:07
 */
public class DCFixedTableData extends DeviceComparisonTable {
    private final List<DeviceComparisonResponseDto> deviceComparisonResponseDtoList;
    private final List<Long> projectIdList;
    private static final Map<String,Object> SIMPLE_FIXED;
    private static final String DEVICE_NAME = "deviceName";
    private static final String CHILDREN = "children";
    static {
        SIMPLE_FIXED = Maps.newHashMap();
        SIMPLE_FIXED.put(DEVICE_NAME,"专业名称");
        SIMPLE_FIXED.put("code","专业编码");
    }
    public DCFixedTableData(List<Long> projectIdList, List<DeviceComparisonResponseDto> deviceComparisonResponseDtoList) {
        this.deviceComparisonResponseDtoList = deviceComparisonResponseDtoList;
        this.projectIdList = projectIdList;
    }

    @Override
    public List<DeviceComparisonTableHeaderDto> getTableHeader() {
        return null;
    }

    @Override
    public List<Map<String, Object>> getTableData() {
        List<Map<String, Object>> resultList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(projectIdList)) {
            List<DeviceComparisonResponseDto> masterList = deviceComparisonResponseDtoList.stream().filter(e -> projectIdList.get(0).equals(e.getEngProjectId())).collect(Collectors.toList());
            beanListToMapList(masterList, String.valueOf(0),resultList);
            for (int i = 1; i < projectIdList.size(); i++) {
                int finalI = i;
                List<DeviceComparisonResponseDto> slaveList = deviceComparisonResponseDtoList.stream().filter(e -> projectIdList.get(finalI).equals(e.getEngProjectId())).collect(Collectors.toList());
                beanListToMapList(slaveList, String.valueOf(i),resultList);
            }
        }
        return resultList;
    }

    protected static <T> List<Map<String, Object>> beanListToMapList(List<T> beanList, String index, List<Map<String, Object>> mapLists) {
        if (beanList != null && beanList.size() > 0) {
            for (T t : beanList) {
                Map<String, Object> e = beanToMap(t, index, mapLists);
                if (mapLists == null) {
                    mapLists = Lists.newArrayList(e);
                } else if (mapLists.size() == 0) {
                    mapLists.add(e);
                } else {
                    boolean isExists = true;
                    for (Map<String, Object> objectMap : mapLists) {
                        if (Objects.equals(objectMap.get(DEVICE_NAME), e.get(DEVICE_NAME))) {
                            objectMap.putAll(e);
                            isExists = false;
                            break;
                        }
                    }
                    if (isExists) {
                        mapLists.add(e);
                    }
                }
            }
        }
        return mapLists;
    }

    protected static <T> Map<String, Object> beanToMap(T bean, String index, List<Map<String, Object>> mapLists) {
        Map<String, Object> map = Maps.newHashMap();
        if (bean != null) {
            BeanMap beanMap = BeanMap.create(bean);
            for (Object key : beanMap.keySet()) {
                if (CHILDREN.equals(key)) {
                    Object o = beanMap.get(key);
                    if (o instanceof List<?>) {
                        String deviceName = beanMap.get(DEVICE_NAME) != null ? beanMap.get(DEVICE_NAME).toString() : "";
                        if (org.springframework.util.CollectionUtils.isEmpty(mapLists)) {
                            map.put(key + "", beanListToMapList((List<?>) o, index, null));
                        }
                        boolean isExists = true;
                        for (Map<String, Object> mapList : mapLists) {
                            if (Objects.equals(mapList.get(DEVICE_NAME), deviceName)) {
                                isExists = false;
                                Object children = mapList.get(CHILDREN);
                                if (children != null) {
                                    map.put(key + "", beanListToMapList((List<?>) o, index, (List<Map<String, Object>>) children));
                                } else {
                                    map.put(key + "", beanListToMapList((List<?>) o, index, null));
                                }
                            }
                        }
                        if (isExists) {
                            map.put(key + "", beanListToMapList((List<?>) o, index, null));
                        }
                    }
                } else if (SIMPLE_FIXED.containsKey(key)) {
                    map.put(key + "", beanMap.get(key));
                } else {
                    map.put(key + index, beanMap.get(key));
                }
            }
        }
        return map;
    }
}
