package cn.savas.hub.module.bdbi.service.device.decorator;


import cn.savas.hub.module.bdbi.api.device.dto.DeviceComparisonTableHeaderDto;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR> zq
 * @description: 抽象基础
 * @date : 2022/4/24 14:23
 */
public class DCFixedTableDecorator extends DeviceComparisonTable {
    private DeviceComparisonTable wrappee;
    public DCFixedTableDecorator(DeviceComparisonTable source) {
        this.wrappee = source;
    }
    @Override
    public List<DeviceComparisonTableHeaderDto> getTableHeader() {
       return wrappee.getTableHeader();
    }

    @Override
    public List<Map<String,Object>> getTableData() {
        return wrappee.getTableData();
    }
}
