package cn.savas.hub.module.bdbi.service.project;

import cn.savas.hub.framework.common.client.enmus.ClientClassIdEnum;
import cn.savas.hub.framework.common.enums.UserTypeEnum;
import cn.savas.hub.framework.common.pojo.PageResult;
import cn.savas.hub.framework.common.util.collection.TreeDataUtil;
import cn.savas.hub.framework.security.core.LoginUser;
import cn.savas.hub.framework.security.core.util.SecurityFrameworkUtils;
import cn.savas.hub.module.bdbi.api.budgetestimate.dto.NormSearchRespDTO;
import cn.savas.hub.module.bdbi.controller.admin.project.vo.*;
import cn.savas.hub.module.bdbi.convert.project.BudgetEstimateConvert;
import cn.savas.hub.module.bdbi.convert.project.ProjectConvert;
import cn.savas.hub.module.bdbi.dal.dataobject.NormCommentsDO;
import cn.savas.hub.framework.common.client.enmus.BillProfessionEnum;
import cn.savas.hub.module.bdbi.enums.budget.ChildTypeEnum;
import cn.savas.hub.module.bdbi.enums.budget.NormTypeEnum;
import cn.savas.hub.module.client.api.project.ClientProjectApi;
import cn.savas.hub.module.client.api.project.ClientProjectFileApi;
import cn.savas.hub.module.client.api.project.dto.ClientProjectReqDTO;
import cn.savas.hub.module.client.api.project.dto.ClientProjectRespDTO;
import cn.savas.hub.module.collect.api.CollectDataApi;
import cn.savas.hub.module.collect.api.dto.BudgetProjectInfoDTO;
import cn.savas.hub.module.collect.api.dto.PrjEngineeringDTO;
import cn.savas.hub.module.collect.api.dto.PrjNormDTO;
import cn.savas.hub.module.collect.api.dto.PrjSectionBillDTO;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.savas.hub.framework.common.client.enmus.ENGTypeEnum.DANWEI;

/**
 * <AUTHOR>
 * @date 2024/11/15 16:28
 */
@Service
public class BudgetEstimateServiceImpl implements BudgetEstimateService {
    @Resource
    private CollectDataApi collectDataApi;
    @Resource
    private ClientProjectFileApi clientProjectFileApi;
    @Resource
    private ClientProjectApi clientProjectApi;
    @Resource
    private NormService normService;
    @Resource
    private NormDataService normDataService;
    @Override
    public PageResult<BudgetEstimatePageRespVO> getProjectPage(BudgetEstimatePageReqVO pageReqVO) {
        // 获取登录用户并校验
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        // 权限检查：非管理员直接返回空结果
        if (!UserTypeEnum.ADMIN.getValue().equals(loginUser.getUserType())) {
            return PageResult.empty();
        }
        // 构建查询对象
        ClientProjectReqDTO projectQuery = new ClientProjectReqDTO();
        projectQuery.setPageSize(pageReqVO.getPageSize());
        projectQuery.setPageNo(pageReqVO.getPageNo());
        projectQuery.setDirectorId(loginUser.getId());
        // 查询项目分页数据
        PageResult<ClientProjectRespDTO> projectPageResult = clientProjectApi.getProjectPageList(projectQuery);
        // 转换并返回结果
        List<BudgetEstimatePageRespVO> resultList = ProjectConvert.INSTANCE.convertList(projectPageResult.getList());
        return new PageResult<>(resultList, projectPageResult.getTotal());
    }

    @Override
    public List<WbsTreeRespVO> getWbsTree(Long projectId) {
        return clientProjectFileApi.changePrjFileSumDs(projectId, () -> {
            // 获取工程结构
            List<WbsTreeRespVO> wbsTree = ProjectConvert.INSTANCE.convertList1(collectDataApi.getWbsTree(projectId));
            for (WbsTreeRespVO wbsTreeRespVO : wbsTree) {
                switch (Objects.requireNonNull(ClientClassIdEnum.getEnumByCode(wbsTreeRespVO.getWbsType()))) {
                    case PROJECT:
                        wbsTreeRespVO.setIcon("wbs_xiangmu");
                        break;
                    case GROUPENG:
                        wbsTreeRespVO.setIcon("wbs_fenbao");
                        break;
                    case SECTIONENG:
                    case SUBSECTIONENG:
                        wbsTreeRespVO.setIcon("wbs_danxiang");
                        break;
                    case UNITENG:
                        wbsTreeRespVO.setIcon("wbs_danwei");
                        break;
                    default:
                        break;
                }
            }
            return TreeDataUtil.baseTreeV2(wbsTree, true, WbsTreeRespVO::getId, WbsTreeRespVO::getPid, WbsTreeRespVO::setChildren, WbsTreeRespVO::getChildren);
        });
    }

    @Override
    public List<BudgetProjectInfoRespVO> getProjectInfo(Long projectId, Long wbsId) {
        return clientProjectFileApi.changePrjFileSumDs(projectId, () -> {
            List<BudgetProjectInfoDTO> projectInfo = collectDataApi.getProjectInfo(wbsId);
            List<BudgetProjectInfoRespVO> respVOS = ProjectConvert.INSTANCE.convertList2(projectInfo.stream().filter(e->(e.getParaState() & 0x04000000) != 0x04000000).collect(Collectors.toList()));
            return TreeDataUtil.baseTreeV2(respVOS, true, BudgetProjectInfoRespVO::getParaId, BudgetProjectInfoRespVO::getParaPid, BudgetProjectInfoRespVO::setChildren, BudgetProjectInfoRespVO::getChildren);
        });
    }

    @Override
    public List<ProjectWbsChildRespVO> getWbsChildTreeList(ProjectWbsChildReqVO reqVO) {
        return clientProjectFileApi.changePrjFileSumDs(reqVO.getProjectId(), () -> {
            // 获取工程结构数据
            List<PrjEngineeringDTO> engList = collectDataApi.getEngineering();
            // 子目数据
            List<PrjNormDTO> normList;
            // 分部数据
            List<PrjSectionBillDTO> branch;
            if (DANWEI.getCode().equals(reqVO.getWbsType())) {
                branch = collectDataApi.getBranch(reqVO.getWbsId());
                normList = collectDataApi.getNorm(reqVO.getWbsId());
            }else {
                branch = collectDataApi.getBranch(null);
                normList = collectDataApi.getNorm(null);
            }
            // 合并数据
            List<ProjectWbsChildRespVO> normTree  = generationNormTree(reqVO, engList, branch, normList);
            // 添加批注
            List<NormCommentsDO> comment = normService.getComments(reqVO.getProjectId(), reqVO.getWbsId(), null);
            Map<Long, NormCommentsDO> tableDataIdMap = comment.stream().collect(Collectors.toMap(NormCommentsDO::getTableDataId, Function.identity()));
            this.addListNormComment(normTree, tableDataIdMap);
            return normTree;
        });
    }

    @Override
    public List<OtherEngFeeRespVO> getProjectOtherEngFee(Long projectId, Long engId) {
        return clientProjectFileApi.changePrjFileSumDs(projectId, () -> {
            List<OtherEngFeeRespVO> respVOS = BudgetEstimateConvert.INSTANCE.convertList1(collectDataApi.getProjectOtherEngFee(engId));
            return TreeDataUtil.baseTree(respVOS, true, OtherEngFeeRespVO::getId, OtherEngFeeRespVO::getPid, OtherEngFeeRespVO::setChildren, OtherEngFeeRespVO::getChildren);
        });
    }

    @Override
    public List<OtherFeeRespVO> getProjectOtherFee(Long projectId, Long engId) {
        return clientProjectFileApi.changePrjFileSumDs(projectId, () -> {
            List<OtherFeeRespVO> respVOS = BudgetEstimateConvert.INSTANCE.convertList2(collectDataApi.getProjectOtherFee(engId));
            return TreeDataUtil.baseTree(respVOS, true, OtherFeeRespVO::getId, OtherFeeRespVO::getPid, OtherFeeRespVO::setChildren, OtherFeeRespVO::getChildren);
        });
    }

    @Override
    public List<TotalEstimateRespVO> getProjectTotalEstimate(Long projectId, Long engId) {
        return clientProjectFileApi.changePrjFileSumDs(projectId, () -> {
            List<TotalEstimateRespVO> respVOList = new ArrayList<>();
            // 添加项目其他费
            respVOList.addAll(BudgetEstimateConvert.INSTANCE.convertList3(collectDataApi.getProjectOtherFee(engId)));
            // 添加工程结构数据
            List<TotalEstimateRespVO> respVOList1 = BudgetEstimateConvert.INSTANCE.convertList4(collectDataApi.getEngineering());
            List<TotalEstimateRespVO> respVOList1Tree = TreeDataUtil.baseTree(respVOList1, true, TotalEstimateRespVO::getId, TotalEstimateRespVO::getPid, TotalEstimateRespVO::setChildren, TotalEstimateRespVO::getChildren);
            // 挂在工程费下面
            respVOList.forEach(e->{
                if ("工程费".equals(e.getFeeName())) {
                    // 添加序号
                    this.addIndexNo(respVOList1Tree, e.getFeeSequence());
                    e.setChildren(respVOList1Tree);
                }
            });
            return TreeDataUtil.baseTree(respVOList, true, TotalEstimateRespVO::getId, TotalEstimateRespVO::getPid, TotalEstimateRespVO::setChildren, TotalEstimateRespVO::getChildren);
        });
    }

    @Override
    public List<Map<String, Object>> getNormSearch(NormSearchReqVO reqVO) {
        return clientProjectFileApi.changePrjFileSumDs(reqVO.getProjectId(), () -> {
            List<Map<String, Object>> integers = new ArrayList<>();
            // 1.获取子目数据
            List<PrjNormDTO> normList = collectDataApi.getNormByCondition(BudgetEstimateConvert.INSTANCE.convert5(reqVO));
            // 2.获取工程结构数据
            List<PrjEngineeringDTO> engList = collectDataApi.getEngineering();
            // 3.获取分部数据
            List<PrjSectionBillDTO> branchList = collectDataApi.getBranch();
            // 4.建筑-安装
            NormSearchReqVO notChange1 = reqVO.clone();
            notChange1.setProfessionalCode(BillProfessionEnum.BUILD.getCode());
            List<NormSearchRespDTO> tableDataBuilding = normDataService.singleProjectOrProject(notChange1, engList, branchList, normList);
            integers.add(ImmutableMap.of("label", "建筑", "value", NormTypeEnum.BUILDING.getCode().toString(), "tableData", tableDataBuilding));

            NormSearchReqVO notChange2 = reqVO.clone();
            notChange2.setProfessionalCode(BillProfessionEnum.INSTALL.getCode());
            List<NormSearchRespDTO> tableDataInstall = normDataService.singleProjectOrProject(notChange2, engList, branchList, normList);
            integers.add(ImmutableMap.of("label", "安装", "value", NormTypeEnum.INSTALL.getCode().toString(), "tableData", tableDataInstall));
            return integers;
        });
    }

    /**
     * 总概算的工程费下级添加序号
     */
    private void addIndexNo(List<TotalEstimateRespVO> totalEstimateRespVOS, String index) {
        if (CollectionUtils.isEmpty(totalEstimateRespVOS)) {
            return;
        }
        for (int i = 0; i < totalEstimateRespVOS.size(); i++) {
            TotalEstimateRespVO estimateRespVO = totalEstimateRespVOS.get(i);
            estimateRespVO.setFeeSequence(index + "." + (i + 1));
            addIndexNo(estimateRespVO.getChildren(), estimateRespVO.getFeeSequence());
        }
    }

    /**
     * 给子目列表添加批注
     */
    private void addListNormComment(List<ProjectWbsChildRespVO> normTree, Map<Long, NormCommentsDO> tableDataIdMap) {
        if (CollectionUtils.isEmpty(normTree)) {
            return;
        }
        for (ProjectWbsChildRespVO respVO : normTree) {
            this.addListNormComment(respVO.getChildren(), tableDataIdMap);
            NormCommentsDO commentRespVO = tableDataIdMap.get(respVO.getId());
            if (commentRespVO != null) {
                respVO.setCommentMark(commentRespVO.getComment().keySet());
            }
        }
    }

    /**
     * 分部子目结构汇总生成
     * @param reqVO
     * @param engList
     * @param branch
     * @param normList
     * @return
     */
    private List<ProjectWbsChildRespVO> generationNormTree(ProjectWbsChildReqVO reqVO,
                                                           List<PrjEngineeringDTO> engList,
                                                           List<PrjSectionBillDTO> branch,
                                                           List<PrjNormDTO> normList) {
        List<ProjectWbsChildRespVO> resultData = Lists.newArrayList();
        switch (Objects.requireNonNull(ClientClassIdEnum.getEnumByCode(reqVO.getWbsType()))) {
            // 执行到单项终止
            case PROJECT:
            case SUBSECTIONENG:
            case SECTIONENG:
                List<PrjEngineeringDTO> engPidFilter = engList.stream().filter(e -> e.getEngPid().equals(reqVO.getWbsId())).collect(Collectors.toList());
                for (PrjEngineeringDTO engDTO : engPidFilter) {
                    reqVO.setWbsId(engDTO.getEngId());
                    reqVO.setWbsType(engDTO.getEngClass());
                    // 转换工程结构数据
                    ProjectWbsChildRespVO engConvert = ProjectConvert.INSTANCE.convert7(engDTO);
                    // 设置返回数据类型
                    engConvert.setWbsType(engDTO.getEngClass());
                    // 设置子目分部类型名称
                    ClientClassIdEnum typeEnum = ClientClassIdEnum.getEnumByCode(engDTO.getEngClass());
                    engConvert.setTypeName(typeEnum == null ? "" : typeEnum.getName());
                    // 递归子数据
                    List<ProjectWbsChildRespVO> childrenList = generationNormTree(reqVO, engList, branch, normList);
                    if (!CollectionUtils.isEmpty(childrenList)) {
                        // 设置子节点
                        engConvert.setChildren(childrenList);
                        resultData.add(engConvert);
                    }
                }
                break;
            case UNITENG:
                List<ProjectWbsChildRespVO> resultList = Lists.newArrayList();
                // 获取分部
                List<PrjSectionBillDTO> billList = branch.stream().
                        filter(e -> e.getBillHostmodel().equals(reqVO.getWbsId())).
                        filter(e -> StringUtils.isBlank(reqVO.getFbName()) ? Boolean.TRUE : e.getBillName().contains(reqVO.getFbName())).
                        collect(Collectors.toList());
                // 分部Id集合
                List<Long> billIdList = billList.stream().map(PrjSectionBillDTO::getBillId).collect(Collectors.toList());
                // 转换分部数据
                resultList.addAll(ProjectConvert.INSTANCE.convertList8(billList));
                // 获取子目
                List<PrjNormDTO> normPidFilter = normList.stream().filter(e -> billIdList.contains(e.getNormPid())).collect(Collectors.toList());
                // 转换子目数据
                resultList.addAll(ProjectConvert.INSTANCE.convertList9(normPidFilter));
                List<ProjectWbsChildRespVO> resultDataTree = TreeDataUtil.toTreeNoParentAdd(resultList, ProjectWbsChildRespVO::getStructureId, ProjectWbsChildRespVO::getStructurePid, ProjectWbsChildRespVO::setChildren, ProjectWbsChildRespVO::getChildren);
                // 删除空分部
                emptyChildrenRm(resultDataTree);
                return resultDataTree;
            default:
                return Collections.emptyList();
        }
        return resultData;
    }

    /**
     * 删除空分部
     */
    private void emptyChildrenRm(List<ProjectWbsChildRespVO> childResponseDtoListTree) {
        if (childResponseDtoListTree != null) {
            Iterator<ProjectWbsChildRespVO> iterator = childResponseDtoListTree.iterator();
            while (iterator.hasNext()) {
                ProjectWbsChildRespVO next = iterator.next();
                if (Objects.equals(next.getChildType(), ChildTypeEnum.SUBSECTION.getCode())) {
                    emptyChildrenRm(next.getChildren());
                    if (CollectionUtils.isEmpty(next.getChildren())) {
                        iterator.remove();
                    }
                }
            }
        }
    }

}
