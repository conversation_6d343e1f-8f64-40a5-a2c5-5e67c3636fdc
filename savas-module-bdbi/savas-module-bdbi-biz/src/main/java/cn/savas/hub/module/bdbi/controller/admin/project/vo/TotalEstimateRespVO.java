package cn.savas.hub.module.bdbi.controller.admin.project.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/12 14:52
 */
@Data
public class TotalEstimateRespVO {
    @Schema(description = "费用ID")
    private Long id;

    @Schema(description = "父级费用ID")
    private Long pid;

    @Schema(description = "主模型ID")
    private Long feeHostmodel;

    @Schema(description = "序号")
    private String feeSequence;

    @Schema(description = "主项号")
    private String feeCode;

    @Schema(description = "名称")
    private String feeName;

    @Schema(description = "规模或工程量")
    private String engExpression;

    /**
     * 设备购置费
     */
    @Schema(description = "设备购置费")
    private BigDecimal emAmount;

    /**
     * 主要材料费
     */
    @Schema(description = "主要材料费")
    private BigDecimal mainMaterialAmount;

    /**
     * 安装费
     */
    @Schema(description = "安装费")
    private BigDecimal installAmount;

    /**
     * 建筑工程费
     */
    @Schema(description = "建筑工程费")
    private BigDecimal projectBuildAmount;

    /**
     * 其他费
     */
    @Schema(description = "其他费")
    private BigDecimal projectOtherAmount;

    /**
     * 合计
     */
    @Schema(description = "合计")
    private BigDecimal projectTotalAmount;

    /**
     * 增值税
     */
    @Schema(description = "增值税")
    private BigDecimal vat;

    /**
     * 含税总计
     */
    @Schema(description = "含税总计")
    private BigDecimal vatTotal;

    /**
     * 投资占比
     */
    @Schema(description = "占比(%)")
    private String projectInvestmentProporttion;

    /**
     * 外币金额
     */
    @Schema(description = "其中外币(万美元)")
    private BigDecimal projectForeignAmount;

    private List<TotalEstimateRespVO> children;
}
