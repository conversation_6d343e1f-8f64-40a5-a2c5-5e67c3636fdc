package cn.savas.hub.module.bdbi.controller.admin.device.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 动态表查询对象
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DynamicTableQuery {
    /**
     * 项目id集合
     */
    private List<Long> prjIdList;

    /**
     * 项目对象集合
     */
    private List<DevicePrjResponseDto> prjList;

    /**
     * 单项名称
     */
    @NotBlank(message = "单项名称不能为空")
    private String singleName;

    public DynamicTableQuery(List<DevicePrjResponseDto> prjList, String singleName) {
        this.prjList = prjList;
        this.singleName = singleName;
    }

}
