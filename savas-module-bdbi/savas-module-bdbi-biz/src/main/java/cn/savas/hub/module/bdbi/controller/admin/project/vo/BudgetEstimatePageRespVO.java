package cn.savas.hub.module.bdbi.controller.admin.project.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/15 17:31
 */
@Data
public class BudgetEstimatePageRespVO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "项目id")
    private Long projectId;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "项目编码")
    private String projectCode;
//
//    @Schema(description = "项目类别")
//    private String projectCategory;
//
//    @Schema(description = "项目状态")
//    private String projectStatus;
//
//    @Schema(description = "工程造价")
//    private String constructionCost;
//
//    @Schema(description = "项目负责人")
//    private String projectLeader;
//
//    @Schema(description = "创建时间")
//    private String createTime;
}
