package cn.savas.hub.module.bdbi.dal.dataobject;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/25 11:26
 */
@TableName(value = "bdbi_norm_comments", autoResultMap = true)
@KeySequence("bdbi_norm_comments_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
public class NormCommentsDO {
    @TableId
    private Long projectId;
    private Long engId;
    private Long tableDataId;
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, String> comment;
}
