package cn.savas.hub.module.bdbi.controller.admin.project.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/11/22 15:06
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProjectWbsChildRespVO {
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "wbsId")
    private Long wbsId;

    @Schema(description = "wbsType")
    private Long wbsType;

    @Schema(description = "wbs和子目类型名称")
    private String typeName;

    @Schema(description = "序号")
    private String indexNo;

    @Schema(description = "编号")
    private String code;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "类型 2分部，0子目，6子设备")
    private Integer childType;

    @Schema(description = "结构id")
    private Long structureId;

    @Schema(description = "结构pid")
    private Long structurePid;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "材质")
    private String material;

    @Schema(description = "单位")
    private String unit;

    @Schema(description = "数量")
    private BigDecimal count;

    @Schema(description = "工程量")
    private String projectCount;

    @Schema(description = "单重")
    private BigDecimal singleWeight;

    @Schema(description = "工程量表达式")
    private String projectCountExpression;

    @Schema(description = "顺序号")
    private Integer sequence;

    @Schema(description = "改换说明")
    private String normQuotietyString;

    @Schema(description = "备注标记")
    private Set<String> commentMark;

    @Schema(description = "价格参数")
    private Map<String, BigDecimal> priceParam;

    @Schema(description = "子项")
    private List<ProjectWbsChildRespVO> children;
}
