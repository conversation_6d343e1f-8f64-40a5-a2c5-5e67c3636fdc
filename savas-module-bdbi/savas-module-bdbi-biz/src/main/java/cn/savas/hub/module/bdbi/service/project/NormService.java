package cn.savas.hub.module.bdbi.service.project;

import cn.savas.hub.module.bdbi.controller.admin.project.vo.NormCommentReqVO;
import cn.savas.hub.module.bdbi.dal.dataobject.NormCommentsDO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/25 11:35
 */
public interface NormService {
    /**
     * 获取子目分部批注
     */
    List<NormCommentsDO> getComments(Long projectId, Long engId, Long tableDataId);

    /**
     * 添加批注
     * @param reqVO
     */
    void addComment(NormCommentReqVO reqVO);
}
