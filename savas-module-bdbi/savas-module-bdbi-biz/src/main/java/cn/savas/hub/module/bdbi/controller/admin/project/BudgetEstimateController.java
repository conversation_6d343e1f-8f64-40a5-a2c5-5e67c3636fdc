package cn.savas.hub.module.bdbi.controller.admin.project;

import cn.savas.hub.framework.common.pojo.CommonResult;
import cn.savas.hub.framework.common.pojo.PageResult;
import cn.savas.hub.module.bdbi.controller.admin.project.vo.*;
import cn.savas.hub.module.bdbi.service.project.BudgetEstimateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import static cn.savas.hub.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @date 2024/11/15 17:05
 */
@Tag(name = "协同前端 - 概算查询")
@RestController
@RequestMapping("/bdbi/budgetEstimate")
public class BudgetEstimateController {
    @Resource
    private BudgetEstimateService budgetEstimateService;


    @Operation(summary = "获取概算项目分页")
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('bdbi:budgetEstimate:query')")
    public CommonResult<PageResult<BudgetEstimatePageRespVO>> getProjectPage(@Valid BudgetEstimatePageReqVO pageReqVO) {
        return success(budgetEstimateService.getProjectPage(pageReqVO));
    }

    @Operation(summary = "获取wbs结构")
    @GetMapping("/getWbsTree")
    @PreAuthorize("@ss.hasPermission('bdbi:budgetEstimate:query')")
    public CommonResult<List<WbsTreeRespVO>> getWbsTree(@RequestParam("projectId") Long projectId) {
        return success(budgetEstimateService.getWbsTree(projectId));
    }

    @Operation(summary = "获取工程概况")
    @GetMapping("/projectInfo")
    @PreAuthorize("@ss.hasPermission('bdbi:budgetEstimate:query')")
    public CommonResult<List<BudgetProjectInfoRespVO>> getProjectInfo(@RequestParam("projectId") Long projectId,
                                                                      @RequestParam("wbsId") Long wbsId) {
        return success(budgetEstimateService.getProjectInfo(projectId, wbsId));
    }

    @Operation(summary = "获取子目列表树 (建筑，安装)")
    @GetMapping("/getWbsChildTreeList")
    @PreAuthorize("@ss.hasPermission('bdbi:budgetEstimate:query')")
    public CommonResult<List<ProjectWbsChildRespVO>> getWbsChildTreeList(ProjectWbsChildReqVO reqVO) {
        return success(budgetEstimateService.getWbsChildTreeList(reqVO));
    }

    @Operation(summary = "其他工程费")
    @GetMapping("/getProjectOtherEngFee")
    @PreAuthorize("@ss.hasPermission('bdbi:budgetEstimate:query')")
    public CommonResult<List<OtherEngFeeRespVO>> getProjectOtherEngFee(@RequestParam Long projectId, @RequestParam Long engId) {
        return success(budgetEstimateService.getProjectOtherEngFee(projectId, engId));
    }

    @Operation(summary = "其他费用")
    @GetMapping("/getProjectOtherFee")
    @PreAuthorize("@ss.hasPermission('bdbi:budgetEstimate:query')")
    public CommonResult<List<OtherFeeRespVO>> getProjectOtherFee(@RequestParam Long projectId, @RequestParam Long engId) {
        return success(budgetEstimateService.getProjectOtherFee(projectId, engId));
    }

    @Operation(summary = "总概算")
    @GetMapping("/getProjectTotalEstimate")
    @PreAuthorize("@ss.hasPermission('bdbi:budgetEstimate:query')")
    public CommonResult<List<TotalEstimateRespVO>> getProjectTotalEstimate(@RequestParam Long projectId, @RequestParam Long engId) {
        return success(budgetEstimateService.getProjectTotalEstimate(projectId, engId));
    }

    @Operation(summary = "子目筛选")
    @GetMapping("/getNormSearch")
    @PreAuthorize("@ss.hasPermission('bdbi:budgetEstimate:query')")
    public CommonResult<List<Map<String, Object>>> getNormSearch(NormSearchReqVO reqVO) {
        return success(budgetEstimateService.getNormSearch(reqVO));
    }
}
