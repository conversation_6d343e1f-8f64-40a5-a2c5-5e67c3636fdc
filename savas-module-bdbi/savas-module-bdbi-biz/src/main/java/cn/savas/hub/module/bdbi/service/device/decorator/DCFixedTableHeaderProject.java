package cn.savas.hub.module.bdbi.service.device.decorator;

import cn.savas.hub.module.bdbi.api.device.dto.DeviceComparisonTableHeaderDto;
import cn.savas.hub.module.bdbi.controller.admin.device.vo.DevicePrjResponseDto;
import com.google.common.collect.Lists;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR> zq
 * @description: 项目列表头
 * @date : 2022/4/24 14:23
 */
public class DCFixedTableHeaderProject extends DCFixedTableDecorator {
    private static final int GROUP_MIN_WIDTH = 670;
    private final List<DevicePrjResponseDto> projectList;
    public DCFixedTableHeaderProject(DeviceComparisonTable source, List<DevicePrjResponseDto> projectList) {
        super(source);
        this.projectList = projectList;
    }

    @Override
    public List<DeviceComparisonTableHeaderDto> getTableHeader() {

        List<DeviceComparisonTableHeaderDto> tableHeader = super.getTableHeader();
        for (int i = 0; i < projectList.size(); i++) {
            DevicePrjResponseDto devicePrjDto = projectList.get(i);

            DeviceComparisonTableHeaderDto projectName = DeviceComparisonTableHeaderDto.builder().field("projectName" + i).title(devicePrjDto.getPrjName() + "（" + (char) (65 + i) + "）").minWidth(GROUP_MIN_WIDTH).build();
            DeviceComparisonTableHeaderDto dxName = setHeaderChildren(projectName, DeviceComparisonTableHeaderDto.builder().field("dxName" + i).title(devicePrjDto.getSingleName()).minWidth(GROUP_MIN_WIDTH).build(), Lists::newArrayList, DeviceComparisonTableHeaderDto::setChildren);
            DeviceComparisonTableHeaderDto designStage = setHeaderChildren(dxName, DeviceComparisonTableHeaderDto.builder().field("designStage" + i).title(devicePrjDto.getDesignStage()).minWidth(GROUP_MIN_WIDTH).build(), Lists::newArrayList, DeviceComparisonTableHeaderDto::setChildren);
            DeviceComparisonTableHeaderDto scale = setHeaderChildren(designStage, DeviceComparisonTableHeaderDto.builder().field("scale" + i).title(getDesignStage(devicePrjDto.getSingleName())).minWidth(GROUP_MIN_WIDTH).build(), Lists::newArrayList, DeviceComparisonTableHeaderDto::setChildren);
            DeviceComparisonTableHeaderDto priceLevel = setHeaderChildren(scale, DeviceComparisonTableHeaderDto.builder().field("priceLevel" + i).title(devicePrjDto.getPriceLevel()).minWidth(GROUP_MIN_WIDTH).build(), Lists::newArrayList, DeviceComparisonTableHeaderDto::setChildren);
            DeviceComparisonTableHeaderDto currencyRate = setHeaderChildren(priceLevel, DeviceComparisonTableHeaderDto.builder().field("currencyRate" + i).title(devicePrjDto.getCurrencyRate()).minWidth(GROUP_MIN_WIDTH).build(), Lists::newArrayList, DeviceComparisonTableHeaderDto::setChildren);
            DeviceComparisonTableHeaderDto monetaryUnit = setHeaderChildren(currencyRate, DeviceComparisonTableHeaderDto.builder().field("monetaryUnit" + i).title(devicePrjDto.getMonetaryUnit()).minWidth(GROUP_MIN_WIDTH).build(), Lists::newArrayList, DeviceComparisonTableHeaderDto::setChildren);
            DeviceComparisonTableHeaderDto isIncludedVat = setHeaderChildren(monetaryUnit, DeviceComparisonTableHeaderDto.builder().field("isIncludedVAT" + i).title(devicePrjDto.getIncludedVAT()).minWidth(GROUP_MIN_WIDTH).build(), Lists::newArrayList, DeviceComparisonTableHeaderDto::setChildren);
            isIncludedVat.setChildren(Lists.newArrayList(
                    DeviceComparisonTableHeaderDto.builder().field("total" + i).title("合计").align("center").minWidth(110).build(),
                    DeviceComparisonTableHeaderDto.builder().field("zygcl" + i).title("主要工程量").align("center").minWidth(110).build(),
                    DeviceComparisonTableHeaderDto.builder().field("sbf" + i).title("设备费").align("center").minWidth(70).build(),
                    DeviceComparisonTableHeaderDto.builder().field("zyclf" + i).title("主要材料费").align("center").minWidth(110).build(),
                    DeviceComparisonTableHeaderDto.builder().field("azf" + i).title("安装费").align("center").minWidth(70).build(),
                    DeviceComparisonTableHeaderDto.builder().field("jzgcf" + i).title("建筑工程费").align("center").minWidth(110).build(),
                    DeviceComparisonTableHeaderDto.builder().field("wbf" + i).title("其中外汇").align("center").minWidth(100).build(),
                    DeviceComparisonTableHeaderDto.builder().field("proportion" + i).title("比例").align("center").minWidth(80).build()
            ));
            tableHeader.add(projectName);
        }
        return tableHeader;
    }

    /**
     * 获取规模
     *
     * @param singleName
     * @return
     */
    public static String getDesignStage(String singleName) {
        StringBuilder resultBuilder = new StringBuilder();
        if (!StringUtils.isEmpty(singleName)) {
            for (byte aByte : singleName.getBytes()) {
                if ((byte) 47 < aByte && aByte < (byte) 58) {
                    resultBuilder.append((char) aByte);
                }
            }
        }
        return resultBuilder.toString();
    }
}
