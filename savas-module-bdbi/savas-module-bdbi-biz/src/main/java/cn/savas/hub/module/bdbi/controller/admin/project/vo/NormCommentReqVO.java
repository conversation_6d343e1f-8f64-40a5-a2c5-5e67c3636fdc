package cn.savas.hub.module.bdbi.controller.admin.project.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/25 15:07
 */
@Data
public class NormCommentReqVO {
    @Schema(description = "项目id")
    @NotNull(message = "项目id不能为空")
    private Long projectId;

    @Schema(description = "单位id")
    @NotNull(message = "单位id不能为空")
    private Long wbsId;

    @Schema(description = "数据id")
    @NotNull(message = "数据id不能为空")
    private Long tableDataId;

    @Schema(description = "批注")
    private Map<String, String> comment;
}
