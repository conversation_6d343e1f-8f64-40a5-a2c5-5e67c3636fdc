package cn.savas.hub.module.bdbi.service.device.decorator;


import cn.savas.hub.module.bdbi.api.device.dto.DeviceComparisonTableHeaderDto;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @description: 表数据序列号
 * @author: zq
 * @date: 2022/4/25 11:07
 */
public class DCFixedTableDataSequence extends DCFixedTableDecorator {


    public DCFixedTableDataSequence(DeviceComparisonTable deviceComparisonTable) {
        super(deviceComparisonTable);
    }

    @Override
    public List<DeviceComparisonTableHeaderDto> getTableHeader() {
        return super.getTableHeader();
    }

    @Override
    public List<Map<String, Object>> getTableData() {
        List<Map<String, Object>> resultList = super.getTableData();
        setSequence(resultList,"");
        return resultList;
    }

    @SuppressWarnings("unchecked")
    private void setSequence(List<Map<String, Object>> mapList, String indexNo) {
        for (int i = 0; i < mapList.size(); i++) {
            String s = indexNo + (i + 1);
            mapList.get(i).put("sequence", s);
            Object children = mapList.get(i).get("children");
            if (Objects.nonNull(children)) {
                setSequence((List<Map<String, Object>>) children, s + ".");
            }
        }
    }

}
