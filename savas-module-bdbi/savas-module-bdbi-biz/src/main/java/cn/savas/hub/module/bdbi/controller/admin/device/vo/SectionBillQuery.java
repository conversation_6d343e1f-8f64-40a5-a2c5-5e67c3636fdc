package cn.savas.hub.module.bdbi.controller.admin.device.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SectionBillQuery {
    /**
     * 项目id集合
     */
    private List<Long> prjIdList;

    /**
     * 项目对象集合
     */
    private List<DevicePrjResponseDto> prjList;

    /**
     * 单项名称
     */
    @NotBlank(message = "单项名称不能为空")
    private String deviceName;

    public SectionBillQuery(){

    }
    public SectionBillQuery(List<Long> prjIdList, String deviceName) {
        this.prjIdList = prjIdList;
        this.deviceName = deviceName;
    }
    public SectionBillQuery(List<Long> prjIdList, String deviceName, List<DevicePrjResponseDto> prjList) {
        this.prjIdList = prjIdList;
        this.deviceName = deviceName;
        this.prjList = prjList;
    }
}
