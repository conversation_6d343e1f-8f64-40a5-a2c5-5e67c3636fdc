package cn.savas.hub.module.bdbi.controller.admin.device;


import cn.savas.hub.framework.common.pojo.CommonResult;
import cn.savas.hub.framework.common.pojo.PageResult;
import cn.savas.hub.module.bdbi.api.device.dto.DeviceComparisonTableHeaderDto;
import cn.savas.hub.module.bdbi.controller.admin.device.vo.*;
import cn.savas.hub.module.bdbi.service.device.DeviceComparisonService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import static cn.savas.hub.framework.common.pojo.CommonResult.success;

/**
 * 概算查询
 *
 * <AUTHOR>
 */
@Tag(name = "协同前端 - 装置对比")
@RestController
@RequestMapping("/bdbi/deviceComparison")
public class DeviceComparisonController {
    @Resource
    private DeviceComparisonService deviceComparisonService;

    @Operation(summary = "装置对比项目分页")
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('bdbi:deviceComparison:query')")
    public CommonResult<PageResult<DeviceComparisonPageRespVO>> getProjectPage(@Valid DeviceComparisonPageReqVO pageReqVO) {
        return success(deviceComparisonService.getProjectPage(pageReqVO));
    }

    @PostMapping("/getPrjList")
    @Operation(summary = "装置对比项目列表")
    @PreAuthorize("@ss.hasPermission('bdbi:deviceComparison:query')")
    public CommonResult<List<DevicePrjResponseDto>> getPrjList(@RequestBody PrjListQuery prjListQuery) {
        List<DevicePrjResponseDto> devicePrjList = deviceComparisonService.getPrjList(prjListQuery);
        return success(devicePrjList);
    }

    @PostMapping("/getDynamicTableHead")
    @Operation(summary = "获取动态表头")
    @PreAuthorize("@ss.hasPermission('bdbi:deviceComparison:query')")
    public CommonResult<List<DeviceComparisonTableHeaderDto>> getDynamicTableHead(@RequestBody DynamicTableQuery dynamicTableQuery) {
        return success(deviceComparisonService.getDynamicTableHead(dynamicTableQuery));
    }

    @PostMapping("/getDynamicTableData")
    @Operation(summary = "获取动态数据")
    @PreAuthorize("@ss.hasPermission('bdbi:deviceComparison:query')")
    public CommonResult<List<Map<String, Object>>> getDynamicTableData(@RequestBody DynamicTableQuery dynamicTableQuery) {
        return success(deviceComparisonService.getDynamicTableData(dynamicTableQuery));
    }
}
