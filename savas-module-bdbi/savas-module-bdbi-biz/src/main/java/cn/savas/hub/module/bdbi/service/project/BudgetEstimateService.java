package cn.savas.hub.module.bdbi.service.project;

import cn.savas.hub.framework.common.pojo.PageResult;
import cn.savas.hub.module.bdbi.controller.admin.project.vo.*;

import java.util.List;
import java.util.Map;

/**
 * 概算查询
 * <AUTHOR>
 * @date 2024/11/15 16:26
 */
public interface BudgetEstimateService {
    PageResult<BudgetEstimatePageRespVO> getProjectPage(BudgetEstimatePageReqVO pageReqVO);

    List<WbsTreeRespVO> getWbsTree(Long projectId);

    List<BudgetProjectInfoRespVO> getProjectInfo(Long projectId, Long wbsId);

    List<ProjectWbsChildRespVO> getWbsChildTreeList(ProjectWbsChildReqVO reqVO);

    List<OtherEngFeeRespVO> getProjectOtherEngFee(Long projectId, Long engId);

    List<OtherFeeRespVO> getProjectOtherFee(Long projectId, Long engId);

    List<TotalEstimateRespVO> getProjectTotalEstimate(Long projectId, Long engId);

    List<Map<String, Object>> getNormSearch(NormSearchReqVO reqVO);
}
