package cn.savas.hub.module.bdbi.service.device.decorator;

import cn.savas.hub.module.bdbi.api.device.dto.DeviceComparisonTableHeaderDto;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @description: 合计差值
 * @author: zq
 * @date: 2022/4/25 11:07
 */
public class DCFixedTableDataDifference extends DCFixedTableDecorator {
    private final List<Long> projectIdList;
    public DCFixedTableDataDifference(DeviceComparisonTable deviceComparisonTable,List<Long> projectIdList) {
        super(deviceComparisonTable);
        this.projectIdList = projectIdList;
    }

    @Override
    public List<DeviceComparisonTableHeaderDto> getTableHeader() {
        return super.getTableHeader();
    }

    @Override
    public List<Map<String, Object>> getTableData() {
        List<Map<String, Object>> resultList = super.getTableData();
        if (!CollectionUtils.isEmpty(resultList)) {
            difference(resultList);
        }
        return resultList;
    }

    @SuppressWarnings("unchecked")
    private void difference(List<Map<String, Object>> resultList) {
        for (int i = 0; i < resultList.size(); i++) {
            Map<String, Object> currentMap = resultList.get(i);
            BigDecimal masterTotal = currentMap.get("total0") == null ? BigDecimal.ZERO : (BigDecimal) currentMap.get("total0");
            for (int j = 1; j < projectIdList.size(); j++) {
                BigDecimal currentDecimal = currentMap.get("total" + j) == null ? BigDecimal.ZERO : (BigDecimal) currentMap.get("total" + j);
                currentMap.put("difference" + j, masterTotal.subtract(currentDecimal));
            }
            Object children = currentMap.get("children");
            if (Objects.nonNull(children)) {
                difference((List<Map<String, Object>>) children);
            }
        }
    }
}
