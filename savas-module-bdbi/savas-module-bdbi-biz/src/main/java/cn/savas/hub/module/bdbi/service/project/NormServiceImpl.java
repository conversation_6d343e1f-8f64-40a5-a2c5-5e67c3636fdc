package cn.savas.hub.module.bdbi.service.project;

import cn.savas.hub.module.bdbi.controller.admin.project.vo.NormCommentReqVO;
import cn.savas.hub.module.bdbi.dal.dataobject.NormCommentsDO;
import cn.savas.hub.module.bdbi.dal.mapper.NormCommentsMapper;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/25 11:36
 */
@DS("master")
@Service
public class NormServiceImpl implements NormService {
    @Resource
    private NormCommentsMapper normCommentsMapper;

    @Override
    public List<NormCommentsDO> getComments(Long projectId, Long engId, Long tableDataId) {
        return normCommentsMapper.selectList(new LambdaQueryWrapper<NormCommentsDO>()
                .eq(NormCommentsDO::getProjectId, projectId)
                .eq(engId != null, NormCommentsDO::getEngId, engId)
                .eq(tableDataId != null, NormCommentsDO::getTableDataId, tableDataId)
        );
    }

    @Override
    public void addComment(NormCommentReqVO reqVO) {
        normCommentsMapper.addRemark(reqVO);
    }
}
