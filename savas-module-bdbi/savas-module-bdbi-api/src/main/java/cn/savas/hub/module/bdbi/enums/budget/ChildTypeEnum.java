package cn.savas.hub.module.bdbi.enums.budget;

import lombok.Getter;

@Getter
public enum ChildTypeEnum {
    /**
     * 分部
     */
    SUBSECTION(2, "分部"),
    /**
     * 子目
     */
    SUBITEM(0, "子目"),
    /**
     * 子设备
     */
    SUBDEVICE(6, "子设备"),

   ;

    private Integer code;
    private String label;


    ChildTypeEnum(Integer code, String label) {
        this.code = code;
        this.label = label;
    }

    public static ChildTypeEnum getByCode(Integer code) {
        for (ChildTypeEnum value : ChildTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
