package cn.savas.hub.module.bdbi.api.device.dto;

import lombok.Getter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
public class DeviceComparisonResponseDto implements Serializable {

    /**
     * 装置名称
     */
    private String deviceName;


    /**
     * 所属项目ID
     */
    private Long engProjectId;

    /**
     * 主要工程量
     */
    private String zygcl;

    /**
     * 合计
     */
    private BigDecimal total;

    /**
     * 设备费
     */
    private BigDecimal sbf;

    /**
     * 主要材料费
     */
    private BigDecimal zyclf;

    /**
     * 安装费
     */
    private BigDecimal azf;

    /**
     * 工程费
     */
    private BigDecimal gcf;

    /**
     * 建筑工程费
     */
    private BigDecimal jzgcf;

    /**
     * 其中外汇
     */
    private BigDecimal wbf;

    /**
     * 比例
     */
    private String proportion;

    /**
     * 装置Id
     */
    private Long engId;

    /**
     * 专业编码
     */
    private String code;

    /**
     * 子节点
     */
    private List<DeviceComparisonResponseDto> children;

    @Getter
    public static class Builder{
        private String deviceName;
        private String zygcl;
        private Long engProjectId;
        private BigDecimal total;
        private BigDecimal sbgzf;
        private BigDecimal jksbgzf;
        private BigDecimal zyclf;
        private BigDecimal jkzyclf;
        private BigDecimal azf;
        private BigDecimal jzgcf;
        private BigDecimal gcf;
        private BigDecimal wbf;
        private String proportion;
        private Long engId;
        private String code;
        private List<DeviceComparisonResponseDto> children;
        public Builder() {

        }
        public Builder deviceName(String deviceName) {
            this.deviceName = deviceName;
            return this;
        }
        public Builder zygcl(String zygcl) {
            this.zygcl = zygcl;
            return this;
        }
        public Builder engProjectId(Long engProjectId) {
            this.engProjectId = engProjectId;
            return this;
        }
        private Builder total(BigDecimal total) {
            this.total = total;
            return this;
        }
        public Builder sbgzf(BigDecimal sbf) {
            this.sbgzf = Objects.nonNull(sbf) ? sbf : BigDecimal.ZERO;
            this.total = this.total != null ? this.total.add(this.sbgzf) : this.sbgzf;
            return this;
        }
        public Builder jksbgzf(BigDecimal jksbf) {
            this.jksbgzf = Objects.nonNull(jksbf) ? jksbf : BigDecimal.ZERO;
            this.total = this.total != null ? this.total.add(this.jksbgzf) : this.jksbgzf;
            this.sbgzf = this.sbgzf != null ? this.sbgzf.add(this.jksbgzf) : this.jksbgzf;
            return this;
        }
        public Builder zyclf(BigDecimal zyclf) {
            this.zyclf = Objects.nonNull(zyclf) ? zyclf : BigDecimal.ZERO;
            this.total = this.total != null ? this.total.add(this.zyclf) : this.zyclf;
            return this;
        }
        public Builder jkzyclf(BigDecimal jkzyclf) {
            this.jkzyclf = Objects.nonNull(jkzyclf) ? jkzyclf : BigDecimal.ZERO;
            this.total = this.total != null ? this.total.add(this.jkzyclf) : this.jkzyclf;
            this.zyclf = this.zyclf != null ? this.zyclf.add(this.jkzyclf) : this.jkzyclf;
            return this;
        }
        public Builder azf(BigDecimal azf) {
            this.azf = Objects.nonNull(azf) ? azf : BigDecimal.ZERO;
            this.total = this.total != null ? this.total.add(this.azf) : this.azf;
            return this;
        }
        public Builder jzgcf(BigDecimal jzgcf) {
            this.jzgcf = Objects.nonNull(jzgcf) ? jzgcf : BigDecimal.ZERO;
            this.total = this.total != null ? this.total.add(this.jzgcf) : this.jzgcf;
            return this;
        }
        public Builder gcf(BigDecimal gcf) {
            this.gcf = Objects.nonNull(gcf) ? gcf : BigDecimal.ZERO;
            return this;
        }
        public Builder wbf(BigDecimal wbf) {
            this.wbf = Objects.nonNull(wbf) ? wbf : BigDecimal.ZERO;
            return this;
        }
        public Builder proportion(String proportion) {
            this.proportion = proportion;
            return this;
        }
        public Builder engId(Long engId) {
            this.engId = engId;
            return this;
        }
        public Builder code(String code) {
            this.code = code;
            return this;
        }
        public Builder children(List<DeviceComparisonResponseDto> children) {
            this.children = children;
            return this;
        }
        public DeviceComparisonResponseDto build() {
            return new DeviceComparisonResponseDto(this);
        }
    }
    private DeviceComparisonResponseDto(Builder builder) {
        this.deviceName = builder.deviceName;
        this.zygcl =  builder.zygcl;
        this.engProjectId = builder.engProjectId;
        this.sbf = builder.sbgzf;
        this.zyclf = builder.zyclf;
        this.azf = builder.azf;
        this.jzgcf = builder.jzgcf;
        this.gcf = builder.gcf;
        this.wbf = builder.wbf;
        this.total = builder.total;
        this.proportion = builder.proportion;
        this.engId = builder.engId;
        this.code = builder.code;
        this.children = builder.children;
    }
}
