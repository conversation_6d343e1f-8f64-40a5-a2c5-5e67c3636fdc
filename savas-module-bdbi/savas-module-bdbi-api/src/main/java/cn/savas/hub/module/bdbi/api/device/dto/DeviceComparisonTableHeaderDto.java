package cn.savas.hub.module.bdbi.api.device.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 概算查询详情表头
 * <AUTHOR>
 */
@Builder
@Getter
@Setter
public class DeviceComparisonTableHeaderDto {
    /**
     * 列字段名
     */
    private String field;

    /**
     * 列标题
     */
    private String title;

    /**
     * 列最小宽度
     */
    private Integer minWidth;

    /**
     * 是否是树节点
     */
    private Boolean treeNode;

    /**
     * 固定列
     *
     * left（固定左侧）, right（固定右侧）
     */
    private String fixed;

    /**
     * 居中
     * left（左对齐）, center（居中对齐）, right（右对齐）
     */
    private String align;

    /**
     * 子节点
     */
    private List<DeviceComparisonTableHeaderDto> children;
}
