package cn.savas.hub.module.bdbi.api.budgetestimate.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/20 11:34
 */
@Data
public class NormSearchRespDTO {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 工程id
     */
    private String engId;

    /**
     * 工程类型
     */
    private Long engClass;

    /**
     * 子目类型名称
     */
    private String normTypeName;

    /**
     * 序号
     */
    private String indexNo;

    /**
     * 编号
     */
    private String code;

    /**
     * 名称
     */
    private String name;


    /**
     * 类型 2分部，0子目
     */
    private Integer normType;

    /**
     * 结构id
     */
    private String structureId;

    /**
     * 结构pid
     */
    private String structurePid;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 材质
     */
    private String material;

    /**
     * 单位
     */
    private String unit;

    /**
     * 数量
     */
    private BigDecimal count;

    /**
     * 工程量
     */
    private String projectCount;

    /**
     * 单重
     */
    private BigDecimal singleWeight;

    /**
     * 价格参数
     */
    private Map<String, BigDecimal> priceParam;

    /**
     * 子集
     */
    private List<NormSearchRespDTO> children;
}
