package cn.savas.hub.module.bdbi.enums.device;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @description: 单位排序
 * @author: zq
 * @date: 2022/5/17 18:44
 */
@Getter
public enum CBSEnum {

    /**
     * 工程费
     */
    CC(0,"CC"),

    /**
     * 总图运输
     */
    GL(1,"GL"),


    /**
     * 建筑物
     */
    AR(2,"AR"),

    /**
     * 构筑物
     */
    CV(3,"CV"),

    /**
     * 静置设备
     */
    FE(4,"FE"),

    /**
     * 机械设备
     */
    PM(5,"PM"),

    /**
     * 工业炉
     */
    HE(6,"HE"),

    /**
     * 金属储罐
     */
    ST(7,"ST"),

    /**
     * 工艺管道
     */
    PI(8,"PI"),

    /**
     * 电气
     */
    EE(9,"EE"),

    /**
     * 电信
     */
    TC(10,"TC"),

    /**
     * 自控仪表
     */
    IN(11,"IN"),

    /**
     * 信息工程
     */
    IT(12,"IT"),

    /**
     * 给排水
     */
    WS(13,"WS"),

    /**
     * 采暖通风
     */
    HV(14,"HV"),

    /**
     * 热工
     */
    TM(15,"TM"),

    /**
     * 分析化验
     */
    AN(16,"AN"),

    /**
     * 催化剂及化学药剂
     */
    PE(17,"PE"),

    /**
     * 劳动安全卫生
     */
    SH(18,"SH"),

    /**
     * 环境工程
     */
    EP(19,"EP"),

    /**
     * 粉体
     */
    FT(21,"FT"),

    /**
     * 钢结构
     */
    GJG(22,"GJG"),

    /**
     * 消防工程
     */
    XF(23,"XF"),

    /**
     * 其他
     */
    QT(24,"QT"),

    /**
     * 安全生产费
     */
    SC(25,"SC");


    /**
     * 编码
     */
    private Integer code;
    /**
     * 值
     */
    private String value;

    public static Map<String, Integer> toMap;

    static {
        toMap = new LinkedHashMap<>();
        for (CBSEnum e : CBSEnum.values()) {
            toMap.put(e.getValue(), e.getCode());
        }
    }
    CBSEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return
     */
    public static CBSEnum getEnumByCode(Integer code){
        for (CBSEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
