package cn.savas.hub.module.bdbi.enums.device;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description: 项目概况
 * @author: zq
 * @date: 2022/10/10 9:06
 */
@Getter
@AllArgsConstructor
public enum ProjectOverviewEnum {
    /**
     * 美元汇率
     */
    CURRENCY_RATE("6.45"),
    /**
     * 货币单位
     */
    MONETARY_UNIT("人民币万元，外汇万美元"),
    /**
     * 是否含增值税
     */
    INCLUDED_VAT("否");


    private final String value;
}
