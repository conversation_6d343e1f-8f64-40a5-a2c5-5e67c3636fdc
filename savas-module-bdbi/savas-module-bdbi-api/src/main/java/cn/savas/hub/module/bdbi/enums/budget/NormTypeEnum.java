package cn.savas.hub.module.bdbi.enums.budget;

import lombok.Getter;

/**
 * wbs类型
 * <AUTHOR>
 * @date 2021-09-01
 */
@Getter
public enum NormTypeEnum {

    /**
     * 建筑专业
     */
    BUILDING(1,"建筑"),

    /**
     * 进口专业
     */
    IMPORT(7,"进口"),

    /**
     * 安装专业
     */
    INSTALL(3,"安装");

    /**
     * 编码
     */
    private Integer code;
    /**
     * 值
     */
    private String value;

    NormTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return
     */
    public static NormTypeEnum getEnumByCode(Integer code){
        if (code != null) {
            for (NormTypeEnum value : values()) {
                if (value.code.equals(code)) {
                    return value;
                }
            }
        }
        return null;
    }

}
