package cn.savas.hub.module.client.controller.desktop.flow.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/2/21 10:46
 */
@Data
public class FlowScheduleRespVO {
    @Schema(description = "流程进度标识")
    private Long alias;

    @Schema(description = "操作标识")
    private Integer operate;

    @Schema(description = "进度id")
    private Long id;

    @Schema(description = "项目id")
    private Long projectid;

    @Schema(description = "绑定工程id")
    private Long hostmodelid;

    @Schema(description = "来源节点ID")
    private Long sourceflownodeid;

    @Schema(description = "目标节点ID")
    private Long targetflownodeid;

    @Schema(description = "进度开始时间")
    private LocalDateTime startdate;

    @Schema(description = "进度完成时间")
    private LocalDateTime submitdate;

    @Schema(description = "来源执行人id")
    private Long sourceuserid;

    @Schema(description = "来源执行人名称")
    private String sourceusername;

    @Schema(description = "目标执行人id")
    private Long targetuserid;

    @Schema(description = "目标执行人名称")
    private String targetusername;

    @Schema(description = "状态值 64：进度执行中 256：进度已经完成 512：进度被终止")
    private Long modelstate;

    @Schema(description = "版本号（不使用）")
    private Integer version;
}
