package cn.savas.hub.module.client.dal.dataobject;

import cn.savas.hub.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/27 14:35
 */
@TableName(value = "client_project_setting", autoResultMap = true)
@KeySequence("client_project_setting_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
public class ClientProjectSettingDO extends BaseDO {
    private Long id;
    private Long projectId;
    private Long engId;
    private String settingType;
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Map<String, Object>> settingValue;
    private Integer version;
    private Boolean isActive;
}
