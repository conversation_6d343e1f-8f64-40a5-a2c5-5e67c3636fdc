package cn.savas.hub.module.client.controller.desktop.flow.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/2/8 11:26
 */
@Data
public class ClientFlowDataScheduleVO extends ClientFlowDataBaseVO{
    @Schema(description = "来源节点ID")
    private Long sourceflownodeid;

    @Schema(description = "目标节点ID")
    private Long targetflownodeid;

    @Schema(description = "进度开始时间")
    private LocalDateTime startdate;

    @Schema(description = "进度完成时间")
    private LocalDateTime submitdate;

    @Schema(description = "来源执行人ID")
    private Long sourceuserid;

    @Schema(description = "来源执行人名称")
    private String sourceusername;

    @Schema(description = "目标执行人ID")
    private Long targetuserid;

    @Schema(description = "目标执行人名称")
    private String targetusername;

    @Schema(description = "版本")
    private Integer version;
}
