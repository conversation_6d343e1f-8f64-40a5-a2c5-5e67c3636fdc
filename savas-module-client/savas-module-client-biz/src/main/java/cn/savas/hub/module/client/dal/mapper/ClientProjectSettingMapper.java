package cn.savas.hub.module.client.dal.mapper;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.savas.hub.module.client.dal.dataobject.ClientProjectSettingDO;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/27 14:45
 */
@Mapper
public interface ClientProjectSettingMapper extends BaseMapperX<ClientProjectSettingDO> {
    default ClientProjectSettingDO selectActiveByType(Long projectId, Long hostmodelid, String settingType) {
        return selectOne(new LambdaQueryWrapperX<ClientProjectSettingDO>()
                .eq(ClientProjectSettingDO::getProjectId, projectId)
                .eq(ClientProjectSettingDO::getSettingType, settingType)
                .eq(ClientProjectSettingDO::getIsActive, true)
                .eq(ClientProjectSettingDO::getEngId, hostmodelid == null ? 0 : hostmodelid) // 如果hostmodelid为null，则查询engId为0的记录
        );
    }

    default void dull(Long id) {
        update(new LambdaUpdateWrapper<ClientProjectSettingDO>()
                .eq(ClientProjectSettingDO::getId, id)
                .set(ClientProjectSettingDO::getIsActive, false));
    }

    default List<ClientProjectSettingDO> selectSettingVersion(Long prjId, Long hostmodelid, String settingtype){
        return selectList(new LambdaQueryWrapperX<ClientProjectSettingDO>()
                .eq(ClientProjectSettingDO::getProjectId, prjId)
                .eq(ClientProjectSettingDO::getIsActive, true)
                .eq(ClientProjectSettingDO::getSettingType, settingtype)
                .eq(hostmodelid != null, ClientProjectSettingDO::getEngId, hostmodelid) // 如果hostmodelid为null，则查询engId为0的记录
        );
    }

    default void deleteByPrjId(Long projectId){
        delete(new LambdaQueryWrapperX<ClientProjectSettingDO>().eq(ClientProjectSettingDO::getProjectId, projectId));
    }
}
