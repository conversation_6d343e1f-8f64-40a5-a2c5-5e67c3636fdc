package cn.savas.hub.module.client.controller.desktop.system.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/1 17:11
 */
@Data
public class ClientFuncPermsRespVO {
    private List<Items> items;
    @Data
    public static class Items{
        private Long alias;
        private Integer operate;
        private Long id;
        private Long parentid;
        private String name;
        private String target;
        private String scripttext;
        private Integer order;
        private Integer style;
        private Integer sorid;
        private Integer group;
        private Integer imageindex;
        private String description;

    }

}
