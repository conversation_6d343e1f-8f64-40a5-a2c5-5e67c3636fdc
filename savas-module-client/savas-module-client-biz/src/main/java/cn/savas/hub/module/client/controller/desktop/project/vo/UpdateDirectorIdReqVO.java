package cn.savas.hub.module.client.controller.desktop.project.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/25 14:00
 */
@Data
public class UpdateDirectorIdReqVO {
    @NotNull(message = "projectid不能为空")
    private Long projectid;
    @NotNull(message = "items不能为空")
    private List<Items> items;

    @Data
    public static class Items {
        private Long engid;
        private Long olddirectorid;
        private Long directorid;
        private String directorname;
    }
}
