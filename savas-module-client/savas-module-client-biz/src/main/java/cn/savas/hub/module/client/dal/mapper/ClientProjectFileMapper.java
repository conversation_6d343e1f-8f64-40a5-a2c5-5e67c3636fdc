package cn.savas.hub.module.client.dal.mapper;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.savas.hub.framework.web.core.util.WebFrameworkUtils;
import cn.savas.hub.module.client.dal.dataobject.ClientProjectFileDO;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @date 2024/11/5 16:31
 */
@Mapper
public interface ClientProjectFileMapper extends BaseMapperX<ClientProjectFileDO> {

    default void deleteByPrjId(Long prjId){
        delete(new LambdaQueryWrapperX<ClientProjectFileDO>().eq(ClientProjectFileDO::getProjectId, prjId));
    }

    default ClientProjectFileDO selectActiveByPrjId(Long prjId, Long userId){
        return selectOne(new LambdaQueryWrapperX<ClientProjectFileDO>()
                .eq(ClientProjectFileDO::getProjectId, prjId)
                .eq(ClientProjectFileDO::getIsActive, true)
                .eq(ClientProjectFileDO::getCreator, userId)
        );
    }


    default ClientProjectFileDO selectIActive(Long prjId){
        return selectOne(new LambdaQueryWrapperX<ClientProjectFileDO>()
                .eq(ClientProjectFileDO::getProjectId, prjId)
                .eq(ClientProjectFileDO::getIsActive, true)
                .eq(ClientProjectFileDO::getCreator, WebFrameworkUtils.getLoginUserId())
        );
    }

    default ClientProjectFileDO selectByPath(String path){
        return selectOne(new LambdaQueryWrapperX<ClientProjectFileDO>().eq(ClientProjectFileDO::getPath, path));
    }
    default void dull(Long id){
        update(new ClientProjectFileDO(), new LambdaUpdateWrapper<ClientProjectFileDO>()
                .eq(ClientProjectFileDO::getId, id)
                .set(ClientProjectFileDO::getIsActive, false));
    }
}
