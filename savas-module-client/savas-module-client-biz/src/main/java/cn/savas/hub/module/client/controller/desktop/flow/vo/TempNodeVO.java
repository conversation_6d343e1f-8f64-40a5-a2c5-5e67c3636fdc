package cn.savas.hub.module.client.controller.desktop.flow.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/7 18:28
 */
@Data
public class TempNodeVO {

    @Schema(description = "流程节点标识")
    private Long alias;

    @Schema(description = "操作标识")
    private Integer operate;

    @Schema(description = "节点id")
    private Long id;

    @Schema(description = "节点名称")
    private String name;

    @Schema(description = "上级节点id")
    private Long parentid;

    @Schema(description = "流程模板id")
    private Long hostmodelid;

    @Schema(description = "角色id")
    private Long ownerid;

    @Schema(description = "类型")
    private Integer style;

    @Schema(description = "主流程ID")
    private Long mainnodeid;

    @Schema(description = "顺序号")
    private Integer sortid;

    @Schema(description = "方向")
    private Items directions;

    @Schema(description = "子节点")
    private List<TempNodeVO> items;

    @Data
    public static class Items {
        @Schema(description = "方向列表", type = "array", implementation = TempNodeDirectionVO.class)
        List<TempNodeDirectionVO> items;
    }
}
