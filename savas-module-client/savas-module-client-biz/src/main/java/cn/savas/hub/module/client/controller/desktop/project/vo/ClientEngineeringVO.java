package cn.savas.hub.module.client.controller.desktop.project.vo;

import cn.savas.hub.framework.common.client.enmus.ClientClassIdEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/7 15:10
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ClientEngineeringVO {
    /**
     * @see ClientClassIdEnum
     */
    private Long alias;
    private Integer operate;
    private String edition;
    private Long createuserid;
    private Integer submittedversion;
    private String createusername;
    private String createtime;
    private String ruletag;
    private String expression;
    private String factors;
    private Integer style;
    private String phase;
    private Integer compiledversion;
    private Long modelstate;
    private String directorname;
    private Long id;
    private Long pid;
    private Long projectid;
    private Long originalid;
    private String name;
    private String code;
    private Long directorid;
    private Integer sortid;

    private List<ClientEngineeringVO> items;
}
