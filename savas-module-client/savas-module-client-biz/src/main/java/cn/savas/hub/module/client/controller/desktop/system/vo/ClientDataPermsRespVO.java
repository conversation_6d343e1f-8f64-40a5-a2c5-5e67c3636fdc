package cn.savas.hub.module.client.controller.desktop.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @date 2024/11/14 11:25
 */
public class ClientDataPermsRespVO {

    @Schema(description = "ID")
    private Long ID;

    @Schema(description = "数据ID")
    private Long HostModelID;

    @Schema(description = "业务对象状态，右数第1位---权限赋予给用户;右数第2位---权限赋予给角色;右数第3位---权限赋予给单位（组织）")
    private Integer ModelState;

    @Schema(description = "角色")
    private String Name;

    @Schema(description = "角色ID")
    private Long OwnerID;

    @Schema(description = "项目ID")
    private Long projectID;


    public Long getID() {
        return ID;
    }

    public void setID(Long ID) {
        this.ID = ID;
    }

    public Long getHostModelID() {
        return HostModelID;
    }

    public void setHostModelID(Long hostModelID) {
        HostModelID = hostModelID;
    }

    public Integer getModelState() {
        return ModelState;
    }

    public void setModelState(Integer modelState) {
        ModelState = modelState;
    }

    public String getName() {
        return Name;
    }

    public void setName(String name) {
        Name = name;
    }

    public Long getOwnerID() {
        return OwnerID;
    }

    public void setOwnerID(Long ownerID) {
        OwnerID = ownerID;
    }

    public Long getProjectID() {
        return projectID;
    }

    public void setProjectID(Long projectID) {
        this.projectID = projectID;
    }
}
