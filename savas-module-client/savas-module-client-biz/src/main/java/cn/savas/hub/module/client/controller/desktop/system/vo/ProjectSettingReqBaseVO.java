package cn.savas.hub.module.client.controller.desktop.system.vo;

import cn.savas.hub.module.client.controller.desktop.system.vo.resolver.SettingAliasTypeIdResolver;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.annotation.JsonTypeIdResolver;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/7/18 14:08
 */
@Data
@JsonTypeInfo(use = JsonTypeInfo.Id.CUSTOM, property = "alias", visible = true)
@JsonTypeIdResolver(SettingAliasTypeIdResolver.class)
public class ProjectSettingReqBaseVO {
    private Long alias;
    private Integer operate;
}
