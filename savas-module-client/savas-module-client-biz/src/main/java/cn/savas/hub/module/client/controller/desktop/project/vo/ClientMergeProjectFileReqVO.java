package cn.savas.hub.module.client.controller.desktop.project.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/5 18:01
 */
@Data
public class ClientMergeProjectFileReqVO {
    @Schema(description = "项目id")
    @NotNull(message = "项目id不能为空")
    private Long projectid;

    @Schema(description = "工程ID")
    private List<Long> hostmodelids;

    @Schema(description = "用户id强制合稿用")
    private Long userid;

    @Schema(description = "增加工程版本")
    private boolean increaseEngVersion;
}
