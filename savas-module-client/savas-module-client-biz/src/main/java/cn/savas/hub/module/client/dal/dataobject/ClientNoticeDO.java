package cn.savas.hub.module.client.dal.dataobject;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/4/15 10:32
 */
@TableName("client_notice")
@KeySequence("client_notice_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
public class ClientNoticeDO{
    @TableId
    private Long noticeId;
    private Long noticeProjectId;
    private Long noticeHostModel;
    private Long noticeUserId;
    private String noticeUserName;
    private LocalDateTime noticeDate;
    private Long noticeTargetUserId;
    private String noticeTargetUserName;
    private String noticeContent;
    private String noticeRuleTag;
    private Long noticeState;
}
