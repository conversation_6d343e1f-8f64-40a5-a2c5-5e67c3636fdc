package cn.savas.hub.module.client.controller.desktop.project.vo;

import cn.savas.hub.module.client.controller.desktop.system.vo.ProjectSettingReqBaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/27 14:47
 */
@Data
public class ProjectSettingReqVO {
    @Schema(description = "项目ID")
    @NotNull(message = "projectid不能为空")
    private Long projectid;;

    @Schema(description = "工程ID")
    private Long hostmodelid;

    @Schema(description = "设置类型")
    @NotNull(message = "设置类型不能为空")
    private String settingtype;

    @Schema(description = "设置参数")
    @NotNull(message = "items不能为空")
    private List<ProjectSettingReqBaseVO> items;
}
