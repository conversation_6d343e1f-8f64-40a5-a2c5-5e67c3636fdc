package cn.savas.hub.module.client.dal.mapper;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.module.client.dal.dataobject.ClientDictBaseFileDO;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @date 2024/11/28 17:41
 */
@Mapper
public interface ClientDictBaseFileMapper extends BaseMapperX<ClientDictBaseFileDO> {
    default ClientDictBaseFileDO selectActiveData(String product, String compilemethod) {
        return selectOne(
                ClientDictBaseFileDO::getIsActive, Boolean.TRUE,
                ClientDictBaseFileDO::getProduct, product,
                ClientDictBaseFileDO::getCompileMethod, compilemethod
        );
    }

    default void dull(Long id) {
        update(new LambdaUpdateWrapper<ClientDictBaseFileDO>()
                .eq(ClientDictBaseFileDO::getId, id)
                .set(ClientDictBaseFileDO::getIsActive, false));
    }
}
