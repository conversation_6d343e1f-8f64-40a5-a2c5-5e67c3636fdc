package cn.savas.hub.module.client.controller.desktop.project.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/12/7 11:37
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ClientProjectFileVersionVO {

    @Schema(description = "总文项目件版本号(提交)")
    private Integer submittedversion;

    @Schema(description = "个人项目件版本号(编制)")
    private Integer compiledversion;

    @Schema(description = "项目ID")
    private Long hostmodelid;
}
