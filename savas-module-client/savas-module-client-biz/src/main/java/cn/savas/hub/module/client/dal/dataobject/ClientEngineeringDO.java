package cn.savas.hub.module.client.dal.dataobject;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/31 14:15
 */
@TableName("client_engineering")
@Data
public class ClientEngineeringDO {
    @TableId
    private Long engId;
    private Long engPid;
    private Long engProjectId;
    private Long engOrginalId;
    private Long engClass;
    private String engCode;
    private String engName;
    private Long engUserId;
    private String engUserName;
    private LocalDateTime engDate;
    private String engSpecialCode;
    private Long engDirectorId;
    private String engDirectorName;
    private Integer engSortId;
    private Integer engCompileVersion;
    private Integer engSyncVersion;
    private Long engState;
    private byte[] engParameters;
    private String engExpression;

    @TableField(exist = false)
    private List<ClientEngineeringDO> children; // 用于树形结构的子节点列表
}
