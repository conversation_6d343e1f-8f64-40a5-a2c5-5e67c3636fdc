package cn.savas.hub.module.client.dal.dataobject;

import cn.savas.hub.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/11/5 16:22
 */
@TableName("client_project")
@KeySequence("client_project_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
public class ClientProjectDO extends BaseDO {
    private Long id;
    private Long projectId;
    private Long projectGroupId;
    private Long originalId;
    private String name;
    private String code;
    private Long alias;
    private Integer operate;
    private String edition;
    private Long createUserId;
    private String createUserName;
    private LocalDateTime clientCreateTime;
    private String ruleTag;
    private String expression;
    private byte[] factors;
    private Integer style;
    private String phase;
    private Integer compiledVersion;
    private Integer syncVersion;
    private Long modelState;
    private String directorName;
    private Long directorId;
    private Integer sortid;
    private String processDefinitionId;
}
