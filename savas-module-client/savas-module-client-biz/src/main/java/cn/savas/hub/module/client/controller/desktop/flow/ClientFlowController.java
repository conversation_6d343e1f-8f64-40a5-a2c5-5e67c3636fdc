package cn.savas.hub.module.client.controller.desktop.flow;

import cn.savas.hub.framework.common.pojo.ClientItemsResult;
import cn.savas.hub.module.client.controller.desktop.flow.vo.*;
import cn.savas.hub.module.client.service.flow.ClientFlowService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/22 16:06
 */
@Tag(name = "协同客户端 - 流程")
@RestController
@RequestMapping("/client/flow")
public class ClientFlowController {
    @Resource
    private ClientFlowService clientFlowService;

    @PostMapping("/saveFlowData")
    @Operation(summary = "保存流程数据")
    public ClientItemsResult<Map> saveFlowData(@Valid @RequestBody ClientFlowDataReqVO saveFlowDataReqVO) {
        clientFlowService.saveFlowData(saveFlowDataReqVO);
        return ClientItemsResult.success(new HashMap<>(0));
    }

    @GetMapping("/getFlowData")
    @Operation(summary = "获取流程数据")
    @Parameter(name = "originalid", description = "项目原始ID", required = true)
    public ClientItemsResult<List<FlowNodeVO>> getFlowData(@RequestParam("originalid") Long originalid) {
        return ClientItemsResult.success(clientFlowService.getFlowData(originalid));
    }

    @GetMapping("/getFlowTemplate")
    @Operation(summary = "获取流程模版")
    public ClientItemsResult<List<ClientFlowTemplateRespVO>> getFlowTemplate() {
        return ClientItemsResult.success(clientFlowService.getFlowTemplate());
    }

    @GetMapping("/getFlowSchedule")
    @Operation(summary = "获取流程进度")
    @Parameter(name = "originalid", description = "项目原始ID", required = true)
    public ClientItemsResult<List<FlowScheduleRespVO>> getFlowSchedule(@RequestParam("originalid") Long originalid) {
        return ClientItemsResult.success(clientFlowService.getFlowSchedule(originalid));
    }

    @GetMapping("/getFlowSuggestion")
    @Operation(summary = "获取校审意见")
    @Parameter(name = "originalid", description = "项目原始ID", required = true)
    public ClientItemsResult<List<FlowSuggestionRespVO>> getFlowSuggestion(@RequestParam("originalid") Long originalid) {
        return ClientItemsResult.success(clientFlowService.getFlowSuggestion(originalid));
    }

    @PostMapping("/lockFlow")
    @Operation(summary = "锁定流程")
    public ClientItemsResult<Map> lockFlow(@Valid @RequestBody ClientFlowLockReqVO reqVO) {
        clientFlowService.lockFlow(reqVO);
        return ClientItemsResult.success(new HashMap<>(0));
    }

    @PostMapping("/unlockFlow")
    @Operation(summary = "解锁流程")
    public ClientItemsResult<Map> unlockFlow(@Valid @RequestBody ClientFlowUnLockReqVO reqVO) {
        clientFlowService.unlockFlow(reqVO);
        return ClientItemsResult.success(new HashMap<>(0));
    }

    @PostMapping("/forceMergeFlow")
    @Operation(summary = "强制合稿")
    public ClientItemsResult<Map> forceMergeFlow(@RequestParam("product")String product,
                                                 @Valid @RequestBody ClientFlowForceMergeReqVO reqVO) {
        clientFlowService.forceMergeFlow(reqVO, product);
        return ClientItemsResult.success(new HashMap<>(0));
    }

    @PostMapping("/bindSumFileVersion")
    @Operation(summary = "节点进度绑定汇总文件版本(绑定的可能是计算前的文件)")
    public ClientItemsResult<Map> bindSumFileVersion(@Valid @RequestBody ClientFlowBindSumFileVersionReqVO reqVO) {
        clientFlowService.bindSumFileVersion(reqVO);
        return ClientItemsResult.success(new HashMap<>(0));
    }
}
