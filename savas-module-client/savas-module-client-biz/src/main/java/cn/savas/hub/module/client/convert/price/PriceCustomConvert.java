package cn.savas.hub.module.client.convert.price;

import cn.savas.hub.module.client.controller.desktop.price.vo.ClientPriceCustomRespVO;
import cn.savas.hub.module.price.api.custom.dto.PublishedCustomPriceDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/13 10:04
 */
@Mapper
public interface PriceCustomConvert {
    PriceCustomConvert INSTANCE = org.mapstruct.factory.Mappers.getMapper(PriceCustomConvert.class);

    @Mappings({
            @Mapping(target = "pricename", source = "priceName"),
            @Mapping(target = "path", source = "path"),
    })
    ClientPriceCustomRespVO convert(PublishedCustomPriceDTO bean);
    List<ClientPriceCustomRespVO> convertList(List<PublishedCustomPriceDTO> bean);
}
