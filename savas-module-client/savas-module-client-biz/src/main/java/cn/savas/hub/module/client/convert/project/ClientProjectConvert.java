package cn.savas.hub.module.client.convert.project;

import cn.savas.hub.framework.common.client.enmus.ClientClassIdEnum;
import cn.savas.hub.framework.common.client.util.ClientDateConvertUtil;
import cn.savas.hub.framework.common.mapstruct.DateTimeConverter;
import cn.savas.hub.module.client.api.project.dto.ClientProjectFileRespDTO;
import cn.savas.hub.module.client.api.project.dto.ClientProjectFileSRespDTO;
import cn.savas.hub.module.client.controller.desktop.project.vo.*;
import cn.savas.hub.module.client.dal.dataobject.*;
import cn.savas.hub.module.collect.api.dto.PrjEngineeringDTO;
import cn.savas.hub.module.collect.api.dto.PrjTestEngineeringDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/6 09:40
 */
@Mapper(uses = DateTimeConverter.class,imports = {java.util.Base64.class, ClientClassIdEnum.class})
public interface ClientProjectConvert {
    ClientProjectConvert INSTANCE = Mappers.getMapper(ClientProjectConvert.class);

    @Mappings({
            @Mapping(target = "factors", expression = "java(parametersToBase64(bean.getFactors()))"),
            @Mapping(target = "id", source = "projectId"),
            @Mapping(target = "projectid", source = "projectGroupId"),
            @Mapping(target = "originalid", source = "originalId"),
            @Mapping(target = "createuserid", source = "createUserId"),
            @Mapping(target = "createusername", source = "createUserName"),
            @Mapping(target = "createtime", source = "clientCreateTime"),
            @Mapping(target = "ruletag", source = "ruleTag"),
            @Mapping(target = "compiledversion", source = "compiledVersion"),
            @Mapping(target = "modelstate", source = "modelState"),
            @Mapping(target = "directorname", source = "directorName"),
            @Mapping(target = "directorid", source = "directorId"),
            @Mapping(target = "alias", expression = "java(ClientClassIdEnum.SERVICE_MIRROR.getCode())"),
            @Mapping(target = "items", expression = "java(new java.util.ArrayList<>())"),

    })
    ClientProjectRespVO convert1(ClientProjectDO bean);

    List<ClientProjectRespVO> convertList1(List<ClientProjectDO> bean);


    @Mappings({
            @Mapping(target = "factors", ignore = true),
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "projectId", source = "id"),
            @Mapping(target = "projectGroupId", source = "projectid"),
            @Mapping(target = "originalId", source = "originalid"),
            @Mapping(target = "createUserId", source = "createuserid"),
            @Mapping(target = "createUserName", source = "createusername"),
            @Mapping(target = "clientCreateTime", source = "createtime"),
            @Mapping(target = "ruleTag", source = "ruletag"),
            @Mapping(target = "compiledVersion", source = "compiledversion"),
            @Mapping(target = "syncVersion", source = "submittedversion"),
            @Mapping(target = "modelState", source = "modelstate"),
            @Mapping(target = "directorName", source = "directorname"),
            @Mapping(target = "directorId", source = "directorid"),

    })
    ClientProjectDO convert2(ClientProjectCreateReqVO.ClientProjectCreateReqDetailVO bean);


    @Mappings({
            @Mapping(target = "hostmodelid", source = "engId")
    })
    ProjectSettingVersionRespVO.Items convert4(ClientProjectSettingDO bean);
    List<ProjectSettingVersionRespVO.Items> convertList4(List<ClientProjectSettingDO> bean);

    ClientProjectFileRespDTO convert5(ClientProjectFileDO bean);

    ClientProjectFileSRespDTO convert6(ClientProjectFileSummaryDO bean);

    ClientProjectFileSummaryDO convert6_1(ClientProjectFileSRespDTO bean);
    @Mappings({
            @Mapping(target = "alias", source = "engClass"),
            @Mapping(target = "operate", constant = "0"),
            @Mapping(target = "createuserid", source = "engUserId"),
            @Mapping(target = "submittedversion", source = "engSyncVersion"),
            @Mapping(target = "createusername", source = "engUserName"),
            @Mapping(target = "createtime", source = "engDate"),
            @Mapping(target = "expression", source = "engExpression"),
            @Mapping(target = "factors", constant = ""),
            @Mapping(target = "compiledversion", source = "engCompileVersion"),
            @Mapping(target = "modelstate", source = "engState"),
            @Mapping(target = "directorname", source = "engDirectorName"),
            @Mapping(target = "id", source = "engId"),
            @Mapping(target = "pid", source = "engPid"),
            @Mapping(target = "name", source = "engName"),
            @Mapping(target = "code", source = "engCode"),
            @Mapping(target = "directorid", source = "engDirectorId"),
            @Mapping(target = "sortid", source = "engSortId"),

    })
    ClientEngineeringVO convert7(ClientEngineeringDO bean);
    List<ClientEngineeringVO> convert7List(List<ClientEngineeringDO> bean);

    default String parametersToBase64(byte[] engParameters){
        return engParameters != null? Base64.getEncoder().encodeToString(engParameters):null;
    }

    @Mappings({
            @Mapping(target = "alias", expression = "java(ClientClassIdEnum.PROJECTENG.getCode())"),
            @Mapping(target = "projectid", source = "projectGroupId"),
            @Mapping(target = "originalid", source = "originalId"),
            @Mapping(target = "createuserid", source = "createUserId"),
            @Mapping(target = "createusername", source = "createUserName"),
            @Mapping(target = "createtime", source = "clientCreateTime"),
            @Mapping(target = "factors", constant = ""),
            @Mapping(target = "style", source = "style"),
            @Mapping(target = "phase", source = "phase"),
            @Mapping(target = "compiledversion", source = "compiledVersion"),
            @Mapping(target = "submittedversion", source = "syncVersion"),
            @Mapping(target = "modelstate", source = "modelState"),
            @Mapping(target = "directorname", source = "directorName"),
            @Mapping(target = "id", source = "projectId"),
            @Mapping(target = "name", source = "name"),
            @Mapping(target = "code", source = "code"),
            @Mapping(target = "directorid", source = "directorId"),

    })
    ClientEngineeringVO convert8(ClientProjectDO bean);
    List<ClientEngineeringVO> convert8List(List<ClientProjectDO> bean);


    @Mappings({
            @Mapping(target = "engId", source = "id"),
            @Mapping(target = "engPid", source = "parentid"),
            @Mapping(target = "engProjectId", source = "projectid"),
            @Mapping(target = "engOrginalId", source = "originalid"),
            @Mapping(target = "engClass", source = "classid"),
            @Mapping(target = "engCode", source = "code"),
            @Mapping(target = "engName", source = "name"),
            @Mapping(target = "engSpecialCode", source = "specialcode"),
            @Mapping(target = "engUserId", source = "createuserid"),
            @Mapping(target = "engUserName", source = "createusername"),
            @Mapping(target = "engDate", source = "createtime"),
            @Mapping(target = "engDirectorId", source = "directorid"),
            @Mapping(target = "engDirectorName", source = "directorname"),
            @Mapping(target = "engSortId", source = "sortid"),
            @Mapping(target = "engCompileVersion", source = "compiledversion"),
            @Mapping(target = "engSyncVersion", source = "submittedversion"),
            @Mapping(target = "engParameters", ignore = true),// base64转数组
            @Mapping(target = "engExpression", source = "expression"),
            @Mapping(target = "engState", source = "modelstate"),
    })
    ClientEngineeringDO convert9(ClientEngineeringReqVO.Items bean);
    List<ClientEngineeringDO> convert9List(List<ClientEngineeringReqVO.Items> bean);

    @Mappings({
            @Mapping(target = "engId", source = "bean.engId"),
            @Mapping(target = "engPid", source = "bean.engPid"),
            @Mapping(target = "engProjectId", expression = "java(projectId)"),
            @Mapping(target = "engOrginalId", source = "bean.engOrginalid"),
            @Mapping(target = "engClass", source = "bean.engClass"),
            @Mapping(target = "engCode", source = "bean.engCode"),
            @Mapping(target = "engName", source = "bean.engName"),
            @Mapping(target = "engUserId", source = "bean.engUserid"),
            @Mapping(target = "engUserName", source = "bean.engUsername"),
            @Mapping(target = "engDate", qualifiedByName = "convertEngDate"),
            @Mapping(target = "engSpecialCode", source = "bean.engSpecialcode"),
            @Mapping(target = "engDirectorId", source = "bean.engDirectorid"),
            @Mapping(target = "engDirectorName", source = "bean.engDirectorname"),
            @Mapping(target = "engSortId", source = "bean.engSortid"),
            @Mapping(target = "engCompileVersion", source = "bean.engCompileversion"),
            @Mapping(target = "engSyncVersion", source = "bean.engSyncversion"),
            @Mapping(target = "engState", source = "bean.engState"),
            @Mapping(target = "engParameters", source = "bean.engParameters"),
            @Mapping(target = "engExpression", source = "bean.engExpression"),
    })
    ClientEngineeringDO convert10(PrjEngineeringDTO bean, @Context Long projectId);
    List<ClientEngineeringDO> convertList10(List<PrjEngineeringDTO> beans, @Context Long projectId);

    @Named("convertEngDate")
    default LocalDateTime convertEngDate(float value) {
        return ClientDateConvertUtil.convertDate(value);
    }

    @Mappings({
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "paraId", source = "id"),
            @Mapping(target = "paraHostmodel", source = "hostmodelid"),
            @Mapping(target = "paraParent", source = "parentid"),
            @Mapping(target = "paraCode", source = "code"),
            @Mapping(target = "paraName", source = "name"),
            @Mapping(target = "paraSortId", source = "sortid"),
            @Mapping(target = "paraEditor", source = "editor"),
            @Mapping(target = "paraStyle", source = "style"),
            @Mapping(target = "paraValueType", source = "datatype"),
            @Mapping(target = "paraInt", source = "intvalue"),
            @Mapping(target = "paraFloat", source = "floatvalue"),
            @Mapping(target = "paraString", source = "strvalue"),
            @Mapping(target = "paraDescription", source = "description"),
            @Mapping(target = "paraContext", source = "context"),
            @Mapping(target = "paraRegular", source = "validateregular"),
            @Mapping(target = "paraRegularText", source = "validatetext"),
            @Mapping(target = "paraCodeItem", source = "codeitem"),
            @Mapping(target = "paraState", source = "modelstate"),

    })
    ClientProjectUserSettingDO convert11(ClientProjectUserSettingReqVO.Items bean);
    List<ClientProjectUserSettingDO> convert11List(List<ClientProjectUserSettingReqVO.Items> bean);

    @Mappings({
            @Mapping(target = "alias", expression = "java(ClientClassIdEnum.MODELPARAMETER.getCode())"),
            @Mapping(target = "operate", constant = "0"),
            @Mapping(target = "id", source = "paraId"),
            @Mapping(target = "hostmodelid", source = "paraHostmodel"),
            @Mapping(target = "parentid", source = "paraParent"),
            @Mapping(target = "code", source = "paraCode"),
            @Mapping(target = "name", source = "paraName"),
            @Mapping(target = "sortid", source = "paraSortId"),
            @Mapping(target = "editor", source = "paraEditor"),
            @Mapping(target = "style", source = "paraStyle"),
            @Mapping(target = "datatype", source = "paraValueType"),
            @Mapping(target = "intvalue", source = "paraInt"),
            @Mapping(target = "floatvalue", source = "paraFloat"),
            @Mapping(target = "strvalue", source = "paraString"),
            @Mapping(target = "description", source = "paraDescription"),
            @Mapping(target = "context", source = "paraContext"),
            @Mapping(target = "validateregular", source = "paraRegular"),
            @Mapping(target = "validatetext", source = "paraRegularText"),
            @Mapping(target = "codeitem", source = "paraCodeItem"),
            @Mapping(target = "modelstate", source = "paraState"),

    })
    ClientProjectUserSettingRespVO convert12(ClientProjectUserSettingDO bean);
    List<ClientProjectUserSettingRespVO> convert12List(List<ClientProjectUserSettingDO> userSettingList);

    @Mappings({
            @Mapping(target = "alias", expression = "java(ClientClassIdEnum.INSPECTIONENGINEERINGCOST.getCode())"),
            @Mapping(target = "operate", constant = "0"),
            @Mapping(target = "id", source = "bean.engId"),
            @Mapping(target = "parentid", source = "bean.engPid"),
            @Mapping(target = "originalid", source = "bean.engOrginalid"),
            @Mapping(target = "code", source = "bean.engCode"),
            @Mapping(target = "specialcode", source = "bean.engSpecialcode"),
            @Mapping(target = "name", source = "bean.engName"),
            @Mapping(target = "sortid", source = "bean.engSortid"),
            @Mapping(target = "factors", expression = "java(parametersToBase64(bean.getEngParameters()))"),
            @Mapping(target = "createuserid", source = "bean.engUserid"),
            @Mapping(target = "createusername", source = "bean.engUsername"),
            @Mapping(target = "createtime", qualifiedByName = "convertEngDate", source = "bean.engDate"),
            @Mapping(target = "modelstate", source = "bean.engState"),
            @Mapping(target = "directorid", source = "bean.engDirectorid"),
            @Mapping(target = "directorname", source = "bean.engDirectorname"),
            @Mapping(target = "compiledversion", source = "bean.engCompileversion"),
            @Mapping(target = "submittedversion", source = "bean.engSyncversion"),
            @Mapping(target = "expression", source = "bean.engExpression"),
    })
    ClientTestEngineeringVO convert13(PrjTestEngineeringDTO bean);
    List<ClientTestEngineeringVO> convertList13(List<PrjTestEngineeringDTO> bean);

}
