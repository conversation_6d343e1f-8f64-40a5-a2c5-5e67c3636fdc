package cn.savas.hub.module.client.controller.desktop.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/12 10:35
 */
@Data
public class ClientNoticeReqVO {
    @NotNull(message = "items不能为空")
    @Schema(description = "通知集合", type = "array", implementation = ClientNoticeReqVO.Items.class)
    private List<Items> items;

    @Data
    public static class Items{
        @Schema(description = "标识")
        private Long alias;
        @Schema(description = "操作类型")
        private Integer operate;
        @Schema(description = "创建人id")
        private Long createuserid;
        @Schema(description = "状态")
        private Integer modelstate;
        @Schema(description = "目标用户名")
        private String targetusername;
        @Schema(description = "创建时间")
        private String createtime;
        @Schema(description = "创建人用户名")
        private String createusername;
        @Schema(description = "项目id")
        private Long projectid;
        @Schema(description = "名称")
        private String name;
        @Schema(description = "规则标签")
        private String ruletag;
        @Schema(description = "工程id")
        private Long hostmodelid;
        @Schema(description = "id")
        private Long id;
        @Schema(description = "目标用户id")
        private Long targetuser;
    }
}
