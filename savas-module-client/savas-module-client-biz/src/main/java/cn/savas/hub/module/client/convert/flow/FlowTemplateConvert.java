package cn.savas.hub.module.client.convert.flow;

import cn.hutool.core.util.ZipUtil;
import cn.savas.hub.module.bpm.api.client.dto.ClientFlowTemplateDTO;
import cn.savas.hub.module.client.controller.desktop.flow.vo.ClientFlowTemplateRespVO;
import cn.savas.hub.module.client.controller.desktop.flow.vo.TempNodeDirectionVO;
import cn.savas.hub.module.client.controller.desktop.flow.vo.TempNodeVO;
import cn.savas.hub.framework.common.client.enmus.ClientClassIdEnum;
import org.apache.batik.transcoder.TranscoderException;
import org.apache.batik.transcoder.TranscoderInput;
import org.apache.batik.transcoder.TranscoderOutput;
import org.apache.batik.transcoder.image.PNGTranscoder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.Base64;
import java.util.List;


@Mapper(imports = {ClientClassIdEnum.class, Base64.class})
public interface FlowTemplateConvert {

    FlowTemplateConvert INSTANCE = Mappers.getMapper(FlowTemplateConvert.class);

    @Mappings({
            @Mapping(target = "alias", expression = "java(ClientClassIdEnum.WORKFLOW.getCode())"),
            @Mapping(target = "operate", constant = "0"),
            @Mapping(target = "id", source = "workId"),
            @Mapping(target = "name", source = "workName"),
            @Mapping(target = "createuserid", source = "workUserId"),
            @Mapping(target = "createusername", source = "workUserName"),
            @Mapping(target = "createtime", source = "workDate"),
            @Mapping(target = "modelstate", source = "workState"),
            @Mapping(target = "sortid", constant = "0"),
            @Mapping(target = "flowchart" , source = "workFlowChart", qualifiedByName = "flowChartConvert"),
    })
    ClientFlowTemplateRespVO convert1(ClientFlowTemplateDTO.FlowTemplate bean);

    @Named("flowChartConvert")
    default String flowChartConvert(byte[] workFlowChart) throws TranscoderException {
        byte[] unWorkFlowChart = ZipUtil.unZlib(workFlowChart);
        // 创建 PNG 转码器
        PNGTranscoder transcoder = new PNGTranscoder();
        // 设置转码提示（可选：处理复杂 SVG）
        transcoder.addTranscodingHint(PNGTranscoder.KEY_FORCE_TRANSPARENT_WHITE, Boolean.TRUE);
        // 设置输入（SVG 文件）
        TranscoderInput input = new TranscoderInput(new ByteArrayInputStream(unWorkFlowChart));
        // 创建字节输出流以捕获 PNG 数据
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        TranscoderOutput output = new TranscoderOutput(outputStream);
        // 执行转换
        transcoder.transcode(input, output);
        // 将 PNG 数据转换为 Base64 编码
        return Base64.getEncoder().encodeToString(ZipUtil.zlib(outputStream.toByteArray(), 3));
    }

    @Mappings({
            @Mapping(target = "alias", expression = "java( ClientClassIdEnum.TEMPLATEFLOWNODE.getCode())"),
            @Mapping(target = "operate", constant = "0"),
            @Mapping(target = "id", source = "nodeId"),
            @Mapping(target = "name", source = "nodeName"),
            @Mapping(target = "parentid", source = "nodePid"),
            @Mapping(target = "hostmodelid", source = "nodeWorkFlow"),
            @Mapping(target = "ownerid", source = "nodeRoleId"),
            @Mapping(target = "style", source = "nodeStyle"),
            @Mapping(target = "mainnodeid", source = "nodeMainId"),
            @Mapping(target = "sortid", source = "nodeSortId"),
            @Mapping(target = "items", expression = "java( new ArrayList<>() )"),
    })
    TempNodeVO convert2(ClientFlowTemplateDTO.FlowNode bean);

    @Mappings({
            @Mapping(target = "alias", expression = "java( ClientClassIdEnum.TEMPLATEFLOWNODEDIRECTION.getCode())"),
            @Mapping(target = "operate", constant = "0"),
            @Mapping(target = "id", source = "dirId"),
            @Mapping(target = "hostmodelid", source = "dirWorkFlow"),
            @Mapping(target = "sourceflownodeid", source = "dirSource"),
            @Mapping(target = "targetflownodeid", source = "dirTarget"),
            @Mapping(target = "description", source = "dirDescription"),
            @Mapping(target = "style", source = "dirStyle"),
    })
    TempNodeDirectionVO convert3(ClientFlowTemplateDTO.FlowDirection bean);




    List<ClientFlowTemplateRespVO> convertList1(List<ClientFlowTemplateDTO.FlowTemplate> beans);
    List<TempNodeVO> convertList2(List<ClientFlowTemplateDTO.FlowNode> beans);
    List<TempNodeDirectionVO> convertList3(List<ClientFlowTemplateDTO.FlowDirection> beans);
}
