package cn.savas.hub.module.client.controller.desktop.project.vo;

import cn.savas.hub.framework.common.client.enmus.ClientClassIdEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/5 18:01
 */
@Data
public class ClientEngineeringReqVO {
    @Schema(description = "新增工程结构", type = "array", implementation = Engineering.class)
    private Engineering inserted;

    @Schema(description = "删除工程结构", type = "array", implementation = Engineering.class)
    private Engineering deleted;

    @Data
    public static class Engineering {
        @JsonProperty("class")
        @Schema(description = "新增工程结构", type = "array", implementation = EngineeringClass.class)
        private List<EngineeringClass> engineeringClass;
    }

    @Data
    public static class EngineeringClass {
        private Long alias;
        @Schema(description = "工程结构", type = "array", implementation = Items.class)
        private List<Items> items;

    }

    @Data
    public static class Items {
        /**
         * @see ClientClassIdEnum
         */
        private Long alias;
        private Long id;
        private Long parentid;
        private Long originalid;
        private Long hostmodelid;
        private Integer classid;
        private String code;
        private String name;
        private String specialcode;
        private Long createuserid;
        private String createusername;
        private LocalDateTime createtime;
        private Integer directorid;
        private String directorname;
        private Integer sortid;
        private Integer compiledversion;
        private Integer submittedversion;
        private Long modelstate;
        private String factors;
        private String expression;
        private Long projectid;
        private String projectname;


        private String option;
        private BigDecimal value;
        private String description;
        private String editor;
        private BigDecimal originalvalue;
    }
}
