package cn.savas.hub.module.client.controller.desktop.flow.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/7 18:28
 */
@Data
public class FlowNodeVO {

    @Schema(description = "流程节点标识")
    private Long alias;

    @Schema(description = "操作标识")
    private Integer operate;

    @Schema(description = "节点id")
    private Long id;

    @Schema(description = "上级节点id")
    private Long parentid;

    @Schema(description = "节点名称")
    private String name;

    @Schema(description = "项目id")
    private Long projectid;

    @Schema(description = "绑定工程ID")
    private Long hostmodelid;

    @Schema(description = "绑定角色id")
    private Long ownerid;

    @Schema(description = "执行人id")
    private Long userid;

    @Schema(description = "执行人名称")
    private String username;

    @Schema(description = "类型 0：开始节点 1：主流程节点 2：完成节点 node_Style")
    private Integer style;

    @Schema(description = "模板ID")
    private Long templateid;

    @Schema(description = "状态值")
    private Integer modelstate;

    @Schema(description = "顺序号")
    private Integer sortid;

    @Schema(description = "方向")
    private Items directions;

    @Data
    @AllArgsConstructor
    public static class Items {
        @Schema(description = "方向列表", type = "array", implementation = FlowNodeDirectionVO.class)
        List<FlowNodeDirectionVO> items;
    }
}
