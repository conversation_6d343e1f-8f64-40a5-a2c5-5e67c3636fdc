package cn.savas.hub.module.client.controller.desktop.flow.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/7 18:28
 */
@Data
public class TempNodeDirectionVO {

    @Schema(description = "流程节点标识")
    private Long alias;

    @Schema(description = "操作标识")
    private Integer operate;

    @Schema(description = "执行方向id")
    private Long id;

    @Schema(description = "流程模板id")
    private Long hostmodelid;

    @Schema(description = "来源节点ID")
    private Long sourceflownodeid; // 来源节点ID

    @Schema(description = "目标节点ID")
    private Long targetflownodeid; // 目标节点ID

    @Schema(description = "说明")
    private String description; // 说明

    @Schema(description = "类型 0：流程执行路径---顺序 1：流程执行路径---同时")
    private Integer style; // 类型 0：流程执行路径---顺序 1：流程执行路径---同时
}
