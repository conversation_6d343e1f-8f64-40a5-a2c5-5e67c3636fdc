package cn.savas.hub.module.client.controller.desktop.flow.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/19 10:04
 */
@Data
public class ClientFlowUnLockReqVO {
    @Schema(description = "项目id")
    @NotNull(message = "项目id不能为空")
    private Long projectid;

    @Schema(description = "解锁工程id集合")
    @NotNull(message = "解锁工程id集合不能为空")
    private List<Long> hostmodelids;
}
