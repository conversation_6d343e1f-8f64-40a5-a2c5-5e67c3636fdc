package cn.savas.hub.module.client.controller.desktop.flow.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/8 11:26
 */
@Data
public class ClientFlowDataNodeVO extends Client<PERSON>lowDataBaseVO{
    @Schema(description = "工程ID或上级节点ID")
    private Long parentid;

    @Schema(description = "节点名称")
    private String name;

    @Schema(description = "绑定的角色ID")
    private Long ownerid;

    @Schema(description = "执行人ID")
    private Long userid;

    @Schema(description = "执行人名称")
    private String username;

    @Schema(description = "类型（0：开始节点，1：主流程节点，2：完成节点）")
    private Integer style;

    @Schema(description = "模板ID")
    private Long templateid;

    @Schema(description = "顺序号")
    private Integer sortid;
}
