package cn.savas.hub.module.client.api.project;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.savas.hub.framework.common.client.util.V9PackageUtil;
import cn.savas.hub.framework.common.client.util.VersionInfo;
import cn.savas.hub.framework.web.core.util.WebFrameworkUtils;
import cn.savas.hub.module.client.api.project.dto.ClientProjectFileRespDTO;
import cn.savas.hub.module.client.api.project.dto.ClientProjectFileSRespDTO;
import cn.savas.hub.module.client.convert.project.ClientProjectConvert;
import cn.savas.hub.module.client.dal.dataobject.ClientProjectDO;
import cn.savas.hub.module.client.dal.dataobject.ClientProjectFileDO;
import cn.savas.hub.module.client.dal.dataobject.ClientProjectFileSummaryDO;
import cn.savas.hub.module.client.dal.mapper.ClientEngineeringMapper;
import cn.savas.hub.module.client.dal.mapper.ClientProjectFileMapper;
import cn.savas.hub.module.client.dal.mapper.ClientProjectFileSummaryMapper;
import cn.savas.hub.module.client.dal.mapper.ClientProjectMapper;
import cn.savas.hub.module.client.dal.redis.RedisKeyConstants;
import cn.savas.hub.module.collect.api.CollectDataApi;
import cn.savas.hub.module.collect.api.dto.PrjEngineeringDTO;
import cn.savas.hub.module.collect.api.dto.PrjProjectEngDTO;
import cn.savas.hub.module.infra.api.file.FileApi;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

import static cn.savas.hub.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.savas.hub.module.client.enums.ErrorCodeConstants.PROJECT_DATASOURCE_SWITCH_FAIL;
import static cn.savas.hub.module.client.enums.ErrorCodeConstants.PROJECT_SUMMARY_FILE_NOT_EXISTS;

/**
 * <AUTHOR>
 * @date 2024/11/19 11:49
 */
@Slf4j
@Service
public class ClientProjectFileApiImpl implements ClientProjectFileApi {
    public static final String DB_STORAGE_PATH = FileUtil.getUserHomePath() + "/savas-hub/sqlite_dbs/";

    @Resource
    private ClientProjectFileMapper clientProjectFileMapper;
    @Resource
    private ClientProjectFileSummaryMapper clientProjectFileSummaryMapper;
    @Resource
    private CollectDataApi collectDataApi;
    @Resource
    private FileApi fileApi;
    @Resource
    private ClientEngineeringMapper clientEngineeringMapper;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private ClientProjectMapper clientProjectMapper;
    private static final long CACHE_EXPIRE_MINUTES = 60;
    @Override
    public ClientProjectFileRespDTO getClientProjectFile(Long projectId) {
        ClientProjectFileDO fileDO = clientProjectFileMapper.selectActiveByPrjId(projectId, WebFrameworkUtils.getLoginUserId());
        return ClientProjectConvert.INSTANCE.convert5(fileDO);
    }

    @Override
    public ClientProjectFileSRespDTO getClientProjectSummaryFile(Long projectId) {
        ClientProjectFileSummaryDO fileDO = clientProjectFileSummaryMapper.selectActiveByPrjId(projectId);
        return ClientProjectConvert.INSTANCE.convert6(fileDO);
    }

    @Override
    public ClientProjectFileRespDTO getLatestProjectFile(Long projectId) {
        ClientProjectFileDO prjId = clientProjectFileMapper.selectActiveByPrjId(projectId, WebFrameworkUtils.getLoginUserId());
        return ClientProjectConvert.INSTANCE.convert5(prjId);
    }

    @Override
    public ClientProjectFileSRespDTO getLatestCollectProjectFile(Long projectId) {
        ClientProjectFileSummaryDO prjId = clientProjectFileSummaryMapper.selectActiveByPrjId(projectId);
        return ClientProjectConvert.INSTANCE.convert6(prjId);
    }

    @Override
    public <T> T changePrjFileDs(Long projectId, Supplier<T> supplier) throws Exception {
        ClientProjectFileRespDTO file = getClientProjectFile(projectId);
        if (file != null) {
            String simpleUUID = IdUtil.simpleUUID();
            byte[] fileContent = fileApi.getFileContent(file.getPath());
            collectDataApi.changeDatasource(simpleUUID, V9PackageUtil.unZipFileToDB(fileContent));
            try {
                return supplier.get();
            }finally {
                collectDataApi.clearDatasource(simpleUUID);
            }
        }
        return null;
    }

    @Override
    public <T> T changePrjFileSumDs(Long projectId, Supplier<T> supplier) {
       return changePrjFileSumDs(projectId, supplier, false);
    }

    @Override
    public <T> T changePrjFileSumDs(Long projectId, Supplier<T> supplier, boolean isWriteBack) {
        ClientProjectFileSRespDTO file = getClientProjectSummaryFile(projectId);
        byte[] fileContent = null;
        String sqliteUrl = null;
        String simpleUUID = IdUtil.simpleUUID();

        try {
            if (file == null) {
                log.warn("[changePrjFileSumDs] 未找到项目文件, projectId: {}", projectId);
                throw exception(PROJECT_SUMMARY_FILE_NOT_EXISTS, projectId);
            }
            fileContent = fileApi.getFileContent(file.getPath());
            sqliteUrl = getProjectSqliteUrl(projectId, fileContent);
            collectDataApi.changeDatasource(simpleUUID, sqliteUrl);
            return supplier.get();
        } catch (Exception e) {
            log.error("[changePrjFileSumDs] 切换数据源失败, projectId: {}", projectId, e);
            throw exception(PROJECT_DATASOURCE_SWITCH_FAIL, e.getLocalizedMessage());
        } finally {
            collectDataApi.clearDatasource(simpleUUID);
            if (isWriteBack && file != null && fileContent != null && sqliteUrl != null) {
                getSelf().writeBackFile(projectId, file, fileContent, sqliteUrl);
                // 写回后更新缓存
                redisTemplate.opsForValue().set(RedisKeyConstants.PROJECT_SUMMARY_FILE_SQLITE_URL + ":" + projectId, sqliteUrl, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void writeBackFile(Long projectId, ClientProjectFileSRespDTO file, byte[] originalContent, String sqliteUrl) {
        VersionInfo versionInfo = V9PackageUtil.getVersionFromFile(originalContent);
        byte[] updatedContent = V9PackageUtil.zipDBToFile(versionInfo, sqliteUrl, null);
        String summaryFileUrl = fileApi.createFile(file.getName(), null, updatedContent);

        ClientProjectFileSummaryDO newFileSummaryDO = new ClientProjectFileSummaryDO();
        newFileSummaryDO.setProjectId(projectId);
        newFileSummaryDO.setName(file.getName());
        newFileSummaryDO.setPath(StringUtils.substringAfterLast(summaryFileUrl, "/"));
        newFileSummaryDO.setUrl(summaryFileUrl);
        newFileSummaryDO.setSize(updatedContent.length);
        newFileSummaryDO.setVersion(file.getVersion() + 1);
        newFileSummaryDO.setIsActive(true);
        // 更新旧文件为非活动
        clientProjectFileSummaryMapper.updateActiveFalse(projectId);
        // 插入新文件
        clientProjectFileSummaryMapper.insert(newFileSummaryDO);
    }

    @Override
    public void insertProjectFileSummary(ClientProjectFileSRespDTO clientProjectFileSRespDTO) {
        ClientProjectFileSummaryDO fileDO = ClientProjectConvert.INSTANCE.convert6_1(clientProjectFileSRespDTO);
        clientProjectFileSummaryMapper.insert(fileDO);
    }

    @Override
    public void updateProjectFileSummary(ClientProjectFileSRespDTO clientProjectFileSRespDTO) {
        ClientProjectFileSummaryDO fileDO = ClientProjectConvert.INSTANCE.convert6_1(clientProjectFileSRespDTO);
        clientProjectFileSummaryMapper.updateById(fileDO);
    }

    @Override
    public void pullProjectFileSummary(Long projectId, String dbPath) {
        List<PrjEngineeringDTO> engineeringList;
        PrjProjectEngDTO projectEng;
        String simpleUUID = IdUtil.simpleUUID();
        try {
            collectDataApi.changeDatasource(simpleUUID, dbPath);
            // 获取临时数据源工程数据
            engineeringList = collectDataApi.getEngineering();
            projectEng = collectDataApi.getProjectEng(projectId);
        } catch (Exception e) {
            log.error("[changePrjFileSumDs]切换数据源失败",e);
            throw exception(PROJECT_DATASOURCE_SWITCH_FAIL);
        }finally {
            collectDataApi.clearDatasource(simpleUUID);
        }
        // 保存工程数据到主数据源
        clientEngineeringMapper.deleteByPrjId(projectId);
        clientEngineeringMapper.insertBatch(ClientProjectConvert.INSTANCE.convertList10(engineeringList, projectId));
        // 保存项目数据到主数据源
        clientProjectMapper.update(null, new LambdaUpdateWrapper<ClientProjectDO>()
                .eq(ClientProjectDO::getProjectId, projectId)
                .set(ClientProjectDO::getCompiledVersion, projectEng.getEngCompileversion())
                .set(ClientProjectDO::getEdition, projectEng.getEngEdition())
                .set(ClientProjectDO::getFactors, projectEng.getEngParameters())
                .set(ClientProjectDO::getSyncVersion, projectEng.getEngSyncversion()));
    }

    @Override
    public void setActiveFalse(Long projectId) {
        clientProjectFileSummaryMapper.updateActiveFalse(projectId);
    }


    /**
     * 同步工程数据到主数据源
     * @param projectId
     * @param engineeringList
     */
    private void syncToMainDatasource(Long projectId, List<PrjEngineeringDTO> engineeringList){
        clientEngineeringMapper.deleteByPrjId(projectId);
        clientEngineeringMapper.insertBatch(ClientProjectConvert.INSTANCE.convertList10(engineeringList, projectId));
    }

    /**
     * 获取 SQLite 数据库文件路径
     * @param projectId
     * @return
     */
    private String getProjectSqliteUrl(Long projectId, byte[] fileContent) {
        String cacheKey = RedisKeyConstants.PROJECT_SUMMARY_FILE_SQLITE_URL + ":" + projectId;
        String sqliteUrl = redisTemplate.opsForValue().get(cacheKey);

        if (sqliteUrl != null && new File(sqliteUrl).exists()) {
            log.debug("[getProjectSqliteUrl] 缓存命中且文件存在, projectId: {}, sqliteUrl: {}", projectId, sqliteUrl);
            return sqliteUrl;
        }

        try {
            String filePath = DB_STORAGE_PATH + "project_" + projectId + ".db";
            V9PackageUtil.unZipFileToDB(fileContent, filePath);
            redisTemplate.opsForValue().set(cacheKey, filePath, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
            log.info("[getProjectSqliteUrl] 缓存 SQLite URL, projectId: {}, sqliteUrl: {}", projectId, filePath);
            return filePath;
        } catch (Exception e) {
            log.error("[getProjectSqliteUrl] 获取 SQLite URL 失败, projectId: {}", projectId, e);
            throw exception(PROJECT_DATASOURCE_SWITCH_FAIL, e.getLocalizedMessage());
        }
    }


    private ClientProjectFileApiImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }
}
