package cn.savas.hub.module.client.controller.desktop.project;

import cn.savas.hub.framework.common.pojo.ClientItemsResult;
import cn.savas.hub.framework.common.pojo.ClientResult;
import cn.savas.hub.module.client.controller.desktop.project.vo.*;
import cn.savas.hub.module.client.service.project.ClientProjectService;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/5 14:42
 */
@Tag(name = "协同客户端 - 项目管理")
@RestController
@RequestMapping("/client/project")
public class ClientProjectController {

    @Resource
    private ClientProjectService clientProjectService;

    @PostMapping("/uploadProjectFile")
    @Operation(summary = "上传项目文件(保存数据)")
    @Parameter(name = "File", description = "文件", required = true)
    @Parameter(name = "engProjectId", description = "项目id", required = true)
    @Parameter(name = "CompiledVersion", description = "个人文件版🙅🏻‍♀️", required = true)
    public ClientResult<Map> uploadProjectFile(@RequestParam("ProjectID") Long engProjectId,
                                               @RequestParam("CompiledVersion") Integer compiledversion,
                                               @RequestParam("uploadfiles") MultipartFile file) throws Exception {
        clientProjectService.uploadProjectFile(engProjectId, file.getOriginalFilename(), file.getBytes(), compiledversion);
        return ClientResult.success(new HashMap(0));
    }

    @GetMapping("/getProjectFile")
    @Operation(summary = "获取个人与汇总项目文件下载地址")
    @Parameters({
            @Parameter(name = "ProjectID", description = "项目id", required = true)
    })
    public ClientResult<ClientProjectFileInfoVO> getProjectFile(@RequestParam("ProjectID") Long engProjectId) {
        return ClientResult.success(clientProjectService.getProjectFile(engProjectId));
    }

    @GetMapping("/downloadProjectFile")
    @Operation(summary = "下载项目文件")
    public void downloadProjectFile(HttpServletResponse response, @RequestParam("filePath") String filePath) throws Exception {
        ClientDownLoadFileReqVO downLoadFileReqVO = new ClientDownLoadFileReqVO();
        downLoadFileReqVO.setFilePath(filePath);
        downLoadFileReqVO.setEngIdList(null);
        clientProjectService.downloadProjectFile(response, downLoadFileReqVO);
    }

    @PostMapping("/downloadHostModelData")
    @Operation(summary = "下载项目文件(支持删除工程数据)")
    @Parameters({
            @Parameter(name = "filePath", description = "文件路径", required = true)
    })
    public void downloadHostModelData(HttpServletResponse response,
                                      @RequestParam("product")String product,
                                      @Valid @RequestBody ClientDownLoadFileReqVO reqVO) throws Exception {
        reqVO.setProduct(product);
        clientProjectService.downloadProjectFile(response, reqVO);
    }

    @PostMapping("/uploadHostModelTableData")
    @Operation(summary = "上传项目数据")
    public ClientResult<Map> uploadHostModelTableData(@Valid @RequestBody ClientProjectCreateReqVO createReqVO) {
        clientProjectService.uploadHostModelTableData(createReqVO);
        return ClientResult.success(new HashMap<>(0));
    }

    @PostMapping("/updateProject")
    @Operation(summary = "修改项目")
    public ClientResult<Map> updateProject(@Valid @RequestBody ClientProjectCreateReqVO createReqVO) {
        clientProjectService.updateProject(createReqVO);
        return ClientResult.success(new HashMap<>(0));
    }

    @PostMapping("/deleteProject")
    @Operation(summary = "删除项目")
    @Parameter(name = "ProjectID", description = "项目id", required = true)
    public ClientResult<Map> deleteProject(@RequestParam("ProjectID") Long engProjectId) {
        clientProjectService.deleteProject(engProjectId);
        return ClientResult.success(new HashMap<>(0));
    }

    @GetMapping("/getProjectList")
    @Operation(summary = "获取项目列表")
    public ClientItemsResult<List<ClientProjectRespVO>> getProjectList() {
        return ClientItemsResult.success(clientProjectService.getProjectList());
    }

    @GetMapping("/getProjectFileVersion")
    @Operation(summary = "获取项目文件版本号")
    @Parameter(name = "ProjectID", description = "项目id", required = true)
    public ClientItemsResult<List<ClientProjectFileVersionVO>> getProjectFileVersion(@RequestParam("ProjectID") Long engProjectId) {
        ClientProjectFileVersionVO fileVersion = clientProjectService.getProjectFileVersion(engProjectId);
        return ClientItemsResult.success(Lists.newArrayList(fileVersion));
    }

    @GetMapping("/getEngineering")
    @Operation(summary = "获取工程结构")
    @Parameter(name = "ProjectID", description = "项目id", required = true)
    public ClientItemsResult<List<ClientEngineeringVO>> getEngineering(@RequestParam("ProjectID") Long engProjectId) {
        return ClientItemsResult.success(clientProjectService.getEngineeringTree(engProjectId));
    }

    @PostMapping("/mergeProjectFile")
    @Operation(summary = "项目文件数据合并")
    public ClientItemsResult<Map> mergeProjectFile(@RequestParam("product")String product,
                                                   @Valid @RequestBody ClientMergeProjectFileReqVO req) {
        clientProjectService.mergeProjectFile(req, product);
        return ClientItemsResult.success(new HashMap<>(0));
    }

    @PostMapping("/copyPrjUploadEngineering")
    @Operation(summary = "上传工程结构数据(复制项目用)")
    public ClientItemsResult<Map> uploadEngineering(@Valid @RequestBody ClientEngineeringReqVO.EngineeringClass req) {
        clientProjectService.copyPrjUploadEngineering(req);
        return ClientItemsResult.success(new HashMap<>(0));
    }

    @PostMapping("/updateDirectorId")
    @Operation(summary = "更换工程负责人")
    public ClientResult<Map> updateDirector(@Valid @RequestBody UpdateDirectorIdReqVO req) {
        clientProjectService.updateDirector(req);
        return ClientResult.success(new HashMap<>(0));
    }

//    @PostMapping("/calculateProjectData")
//    @Operation(summary = "项目文件计算")
//    public ClientItemsResult<Map> calculateProjectData(@RequestParam("product")String product,
//                                                       @Valid @RequestBody ClientMergeProjectFileReqVO req) {
//        req.setProduct(product);
//        clientProjectService.calculateProjectData(req);
//        return ClientItemsResult.success(new HashMap<>(0));
//    }

    @PostMapping("/saveProjectUserSetting")
    @Operation(summary = "保存项目用户设置")
    public ClientResult<Map> saveProjectUserSetting(@Valid @RequestBody ClientProjectUserSettingReqVO reqVO) {
        clientProjectService.saveProjectUserSetting(reqVO);
        return ClientResult.success(new HashMap<>(0));
    }

    @GetMapping("/getProjectUserSetting")
    @Operation(summary = "获取项目用户设置")
    @Parameter(name = "ProjectID", description = "项目id", required = true)
    public ClientItemsResult<List<ClientProjectUserSettingRespVO>> getProjectUserSetting(@RequestParam("projectid") Long projectId) {
        return ClientItemsResult.success(clientProjectService.getProjectUserSetting(projectId));
    }

    @GetMapping("/getSumFileByVersion")
    @Operation(summary = "根据版本获取汇总文件地址")
    @Parameters({
            @Parameter(name = "projectid", description = "项目id", required = true),
            @Parameter(name = "version", description = "文件版本", required = true)
    })
    public ClientItemsResult<String> getSumFileByVersion(@RequestParam("projectid") Long engProjectId,
                                                         @RequestParam("version") Integer version) {
        return ClientItemsResult.success(clientProjectService.getSumFileByVersion(engProjectId, version));
    }

    @GetMapping("/getTestEngineering")
    @Operation(summary = "获取工程检验检测费")
    @Parameters({
            @Parameter(name = "projectid", description = "项目id", required = true)
    })
    public ClientItemsResult<List<ClientTestEngineeringVO>> getTestEngineering(@RequestParam("projectid") Long projectid) {
        return ClientItemsResult.success(clientProjectService.getTestEngineering(projectid));
    }
}
