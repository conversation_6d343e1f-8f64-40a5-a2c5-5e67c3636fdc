package cn.savas.hub.module.client.controller.desktop.flow.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27 18:15
 */
@Data
public class ClientFlowForceMergeReqVO {
    @Schema(description = "项目id")
    private Long projectid;

    @Schema(description = "强制合并用户列表")
    private List<ForceMergeReqVOItem> items;
    @Data
    public static class ForceMergeReqVOItem {
        @Schema(description = "用户ID")
        @NotNull
        private Long userid;

        @Schema(description = "工程ID")
        private Long engid;
    }
}
