package cn.savas.hub.module.client.controller.desktop.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/24 16:57
 */
@Data
public class ClientLoginRespVO {
    @Schema(description = "用户信息")
    private List<Items> items;

    private Integer value;

    @Schema(description = "访问令牌")
    private String accessToken;

    @Schema(description = "唯一标识")
    private Long workerid;

    public static class Items {
        @Schema(description = "用户id")
        private Long id;
        @Schema(description = "用户id")
        private String loginname;
        @Schema(description = "用户名称")
        private String name;
        @Schema(description = "组织名称")
        private String organization;

        @Schema(description = "过期时间")
        private LocalDateTime expiresTime;

        @Schema(description = "最后登录时间")
        private LocalDateTime lastpasswordtime;

        private Long alias;

        private Integer operate;

        // getter/setter

        public LocalDateTime getLastpasswordtime() {
            return lastpasswordtime;
        }

        public void setLastpasswordtime(LocalDateTime lastpasswordtime) {
            this.lastpasswordtime = lastpasswordtime;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getLoginname() {
            return loginname;
        }

        public void setLoginname(String loginname) {
            this.loginname = loginname;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getOrganization() {
            return organization;
        }

        public void setOrganization(String organization) {
            this.organization = organization;
        }

        public LocalDateTime getExpiresTime() {
            return expiresTime;
        }

        public void setExpiresTime(LocalDateTime expiresTime) {
            this.expiresTime = expiresTime;
        }

        public Long getAlias() {
            return alias;
        }

        public void setAlias(Long alias) {
            this.alias = alias;
        }

        public Integer getOperate() {
            return operate;
        }

        public void setOperate(Integer operate) {
            this.operate = operate;
        }
    }
}
