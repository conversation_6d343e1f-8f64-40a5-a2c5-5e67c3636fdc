package cn.savas.hub.module.client.dal.dataobject;

import cn.savas.hub.framework.common.client.enmus.ClientClassIdEnum;
import cn.savas.hub.framework.mybatis.core.dataobject.BaseDO;
import cn.savas.hub.module.client.enums.project.DataPermsEnum;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/11/14 11:28
 */
@TableName("client_data_perms")
@KeySequence("client_data_perms_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
public class ClientDataPermsDO extends BaseDO {
    private Long id;
    private Long userId;
    /**
     * 权限数据ID
     */
    private Long modelId;
    /**
     * 权限数据类型
     */
    private ClientClassIdEnum modelType;
    /**
     * 项目ID
     */
    private Long projectId;
    /**
     * 权限类型：只读、读写、管理
     */
    private DataPermsEnum permsType;
    /**
     * 是否继承自父节点
     */
    private Integer isInherited;
}
