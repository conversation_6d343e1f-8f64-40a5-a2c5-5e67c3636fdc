package cn.savas.hub.module.client.controller.desktop.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2024/10/24 15:29
 */
@Data
public class ClientLoginReqVO {

    @Schema(description = "用户名")
    @NotEmpty(message = "用户名不能为空")
    @JsonProperty("LoginName")
    private String loginName;

    @Schema(description = "密码")
    @NotEmpty(message = "密码不能为空")
    @JsonProperty("Password")
    private String password;
}
