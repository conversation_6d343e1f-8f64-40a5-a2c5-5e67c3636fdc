package cn.savas.hub.module.client.controller.desktop.system.vo.resolver;

import cn.savas.hub.framework.common.client.enmus.ClientClassIdEnum;
import cn.savas.hub.module.client.controller.desktop.system.vo.*;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.DatabindContext;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.jsontype.impl.TypeIdResolverBase;

/**
 * <AUTHOR>
 * @date 2025/2/8 11:37
 */
public class SettingAliasTypeIdResolver extends TypeIdResolverBase {
    private static final String MODEL_PARAMETER = ClientClassIdEnum.MODELPARAMETER.getCode().toString();
    private static final String RATE_STANDARD = ClientClassIdEnum.RATESTANDARD.getCode().toString();
    private static final String EFFECT_FEE_SET = ClientClassIdEnum.EFFECTFEESET.getCode().toString();
    private static final String EFFECT_FEE_COEFFICIENT_MULTI = ClientClassIdEnum.EFFECTFEECOEFFICIENTMULTI.getCode().toString();
    private static final String MATERIAL_COEFFICIENT = ClientClassIdEnum.MATERIALCOEFFICIENT.getCode().toString();

    @Override
    public String idFromValue(Object value) {
        if (value instanceof ClientPrjSettingParamVO) return MODEL_PARAMETER;
        if (value instanceof ClientPrjSettingRateVO) return RATE_STANDARD;
        if (value instanceof ClientPrjSettingMaterialVO) return MATERIAL_COEFFICIENT;
        if (value instanceof ClientPrjSettingEffectSetVO) return EFFECT_FEE_SET;
        if (value instanceof ClientPrjSettingEffectMultiVO) return EFFECT_FEE_COEFFICIENT_MULTI;
        return null;
    }

    @Override
    public String idFromValueAndType(Object value, Class<?> suggestedType) {
        return idFromValue(value);
    }

    @Override
    public JavaType typeFromId(DatabindContext context, String id) {
        if (MODEL_PARAMETER.equals(id)) {
            return context.constructType(ClientPrjSettingParamVO.class);
        }else if (RATE_STANDARD.equals(id)) {
            return context.constructType(ClientPrjSettingRateVO.class);
        }else if (MATERIAL_COEFFICIENT.equals(id)) {
            return context.constructType(ClientPrjSettingMaterialVO.class);
        }else if(EFFECT_FEE_SET.equals(id)){
            return context.constructType(ClientPrjSettingEffectSetVO.class);
        }else if(EFFECT_FEE_COEFFICIENT_MULTI.equals(id)){
            return context.constructType(ClientPrjSettingEffectMultiVO.class);
        } else {
            throw new IllegalArgumentException("Unknown alias: " + id);
        }
    }

    @Override
    public JsonTypeInfo.Id getMechanism() {
        return JsonTypeInfo.Id.CUSTOM;
    }
}
