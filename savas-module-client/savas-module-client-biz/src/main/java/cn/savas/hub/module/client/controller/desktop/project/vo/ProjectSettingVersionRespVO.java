package cn.savas.hub.module.client.controller.desktop.project.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/27 16:31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectSettingVersionRespVO {

    private List<Items> items;
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Items{
        @Schema(description = "工程id")
        private Long hostmodelid;
        @Schema(description = "版本")
        private Integer version;
    }
}
