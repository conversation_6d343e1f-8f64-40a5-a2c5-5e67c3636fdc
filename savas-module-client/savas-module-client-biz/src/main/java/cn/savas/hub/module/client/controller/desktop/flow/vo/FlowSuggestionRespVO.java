package cn.savas.hub.module.client.controller.desktop.flow.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/2/20 12:01
 */
@Data
public class FlowSuggestionRespVO {
    @Schema(description = "流程意见标识")
    private Long alias;

    @Schema(description = "操作标识")
    private Integer operate;

    @Schema(description = "意见id")
    private Long id;

    @Schema(description = "项目id")
    private Long projectid;

    @Schema(description = "绑定工程id")
    private Long hostmodelid;

    @Schema(description = "针对的对象ID，对应的子目或分部的ID")
    private Long modelid;

    @Schema(description = "流程进度ID")
    private Long scheduleid;

    @Schema(description = "内容（相当于V8中的*校审意见*）")
    private String content;

    @Schema(description = "批注，回复内容（相当于V8中的*修改说明*）")
    private String comment;

    @Schema(description = "提交日期（相当于V8中的*校审时间*）")
    private LocalDateTime submittime;

    @Schema(description = "回复日期（相当于V8中的*修改时间*）")
    private LocalDateTime commenttime;

    @Schema(description = "创建人ID（相当于V8中的*校审人*）")
    private Long createuserid;

    @Schema(description = "创建人名称")
    private String createusername;

    @Schema(description = "创建时间")
    private LocalDateTime createtime;

    @Schema(description = "顺序号")
    private Integer sortid;

    @Schema(description = "状态值 1：意见提交生效 2：意见已被回复")
    private Long modelstate;

    @Schema(description = "流程节点名称")
    private String flownodename;

    @Schema(description = "目标流程节点名称")
    private String targetflownodename;
}
