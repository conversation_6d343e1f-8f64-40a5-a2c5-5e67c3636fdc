package cn.savas.hub.module.client.dal.mapper;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.savas.hub.module.client.dal.dataobject.ClientEngineeringDO;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/31 15:56
 */
@Mapper
public interface ClientEngineeringMapper extends BaseMapperX<ClientEngineeringDO> {
    default List<ClientEngineeringDO> selectByDirectorId(Long loginUserId){
        return selectList(new LambdaQueryWrapperX<ClientEngineeringDO>()
                .eq(ClientEngineeringDO::getEngDirectorId, loginUserId)
        );
    }

    default List<ClientEngineeringDO>  selectByPrjId(Long engProjectId){
        return selectList(new LambdaQueryWrapperX<ClientEngineeringDO>()
                .eq(ClientEngineeringDO::getEngProjectId, engProjectId)
        );
    }

    List<ClientEngineeringDO> selectByPrjIdTree(Long engProjectId);

    default void deleteByEngIds(List<Long> ids){
        delete(new LambdaQueryWrapperX<ClientEngineeringDO>().in(ClientEngineeringDO::getEngId, ids));
    }

    default void deleteByPrjId(Long prjId){
        delete(new LambdaQueryWrapperX<ClientEngineeringDO>().eq(ClientEngineeringDO::getEngProjectId, prjId));
    }

    default void updateDirectorId(Long projectid, Long engid, Long directorid, String directorname){
        update(new LambdaUpdateWrapper<ClientEngineeringDO>()
                .eq(ClientEngineeringDO::getEngProjectId, projectid)
                .eq(ClientEngineeringDO::getEngId, engid)
                .set(ClientEngineeringDO::getEngDirectorId, directorid)
                .set(ClientEngineeringDO::getEngDirectorName, directorname)
        );
    }
}
