package cn.savas.hub.module.client.controller.desktop.system;

import cn.savas.hub.framework.common.pojo.ClientResult;
import cn.savas.hub.framework.websocket.core.sender.WebSocketMessageSender;
import cn.savas.hub.module.client.controller.desktop.project.vo.*;
import cn.savas.hub.module.client.controller.desktop.system.vo.ClientSettingRespVO;
import cn.savas.hub.module.client.enums.project.ProjectSettingEnum;
import cn.savas.hub.module.client.service.system.ClientConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/28 17:47
 */
@Tag(name = "协同客户端 - 配置管理")
@RestController
@RequestMapping("/client/config")
@Validated
public class ClientConfigController {
    @Resource
    private ClientConfigService clientConfigService;
    @PostMapping("/saveProjectSetting")
    @Operation(summary = "保存项目设置")
    public ClientResult<Map<String, Object>> saveProjectSetting(@RequestParam("product") String product,
                                                                @Valid @RequestBody ProjectSettingReqVO createReqVO) {
        return ClientResult.success(clientConfigService.saveProjectSetting(product, createReqVO));
    }

    @GetMapping("/getProjectSetting")
    @Operation(summary = "获取项目设置")
    @Parameter(name = "settingtype", description = "rate_setting:费率设置, project_setting:项目设置, material_setting:主材系数设置, effect_fee_set:装置费用降效设置", required = true)
    public ClientResult<ClientSettingRespVO> getProjectSetting(@RequestParam("projectid") Long engProjectId,
                                                               @RequestParam(value = "hostmodelid", required = false) Long hostmodelid,
                                                               @RequestParam("settingtype") String settingtype) {
        ProjectSettingEnum settingEnum = ProjectSettingEnum.getByCode(settingtype);
        return ClientResult.success(clientConfigService.getProjectSettingActive(engProjectId, hostmodelid, settingEnum));
    }

    @GetMapping("/getProjectSettingVersion")
    @Operation(summary = "获取项目配置版本信息")
    @Parameter(name = "settingtype", description = "rate_setting:费率设置, project_setting:项目设置, material_setting:主材系数设置, effect_fee_set:装置费用降效设置", required = true)
    public ClientResult<ProjectSettingVersionRespVO> getProjectSettingVersion(@RequestParam("projectid") Long projectID,
                                                                              @RequestParam(value = "hostmodelid", required = false) Long hostmodelid,
                                                                              @RequestParam("settingtype") String settingtype) {
        ProjectSettingEnum settingEnum = ProjectSettingEnum.getByCode(settingtype);
        return ClientResult.success(clientConfigService.getProjectSettingVersion(projectID, hostmodelid, settingEnum));
    }

    @PostMapping("/saveDictFile")
    @Operation(summary = "保存字典库文件")
    @Parameter(name = "uploadfiles", description = "文件", required = true)
    public ClientResult<Boolean> saveDictFile(@RequestParam("uploadfiles") MultipartFile file,
                                              @RequestParam("uploadtime") Long uploadtime,
                                              @RequestParam("product") String product,
                                              @RequestParam("compilemethod") String compilemethod) throws IOException {
        return ClientResult.success(clientConfigService.saveDictFile(file, uploadtime, product, compilemethod));
    }
    @GetMapping("/getDictFile")
    @Operation(summary = "获取最新字典库文件")
//    @PreAuthorize("@ss.hasClientPermission('client:config:getdictfile')")
    public void getDictFile(HttpServletResponse response,
                            @RequestParam("product") String product,
                            @RequestParam("compilemethod") String compileMethod) throws Exception {
        clientConfigService.getDictFile(response, product, compileMethod);
    }
    @GetMapping("/getDictSettingVersion")
    @Operation(summary = "获取字典配置版本信息")
    public ClientResult<DictSettingVersionRespVO> getDictSettingVersion(@RequestParam("product") String product,
                                                                        @RequestParam("compilemethod") String compileMethod) {
        return ClientResult.success(clientConfigService.getDictSettingVersion(product, compileMethod));
    }

}
