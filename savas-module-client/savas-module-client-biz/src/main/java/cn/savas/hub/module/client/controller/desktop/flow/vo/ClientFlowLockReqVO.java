package cn.savas.hub.module.client.controller.desktop.flow.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/5/19 10:04
 */
@Data
public class ClientFlowLockReqVO {
    @Schema(description = "项目id")
    @NotNull(message = "项目id不能为空")
    private Long projectid;

    @Schema(description = "流程节点id集合")
    private List<Long> flownodeids;

    @Schema(description = "删除工程id集合")
    private List<Long> delhostmodelids;

    @Schema(description = "修改工程id集合")
    private List<Long> edithostmodelids;
}
