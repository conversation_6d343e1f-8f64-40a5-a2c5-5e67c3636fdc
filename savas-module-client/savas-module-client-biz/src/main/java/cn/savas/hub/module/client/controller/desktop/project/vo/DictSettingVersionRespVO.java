package cn.savas.hub.module.client.controller.desktop.project.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/11/27 16:31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DictSettingVersionRespVO {

    @Schema(description = "字典文件上传时间")
    private Long uploadtime;
}
