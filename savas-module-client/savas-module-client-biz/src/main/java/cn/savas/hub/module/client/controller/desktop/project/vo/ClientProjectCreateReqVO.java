package cn.savas.hub.module.client.controller.desktop.project.vo;

import cn.savas.hub.framework.common.client.enmus.ClientClassIdEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/5 18:01
 */
@Data
public class ClientProjectCreateReqVO {
    @Schema(description = "项目", type = "array", implementation = ClientProjectCreateReqDetailVO.class)
    @Size(min = 1, message = "项目不能为空")
    private List<ClientProjectCreateReqDetailVO> items;

    @Data
    public static class ClientProjectCreateReqDetailVO {
        /**
         * @see ClientClassIdEnum
         */
        private Long alias;
        private Integer operate;
        private String edition;
        private Long createuserid;
        private Integer submittedversion;
        private String createusername;
        private String createtime;
        private String ruletag;
        private String expression;
        private String factors;
        private Integer style;
        private String phase;
        private Integer compiledversion;
        private Long modelstate;
        private String directorname;
        @Schema(description = "项目id")
        private Long id;
        @Schema(description = "项目分组id")
        @NotEmpty(message = "项目id不能为空")
        private Long projectid;
        @Schema(description = "项目原始ID")
        private Long originalid;
        @Schema(description = "项目名称")
        @NotEmpty(message = "项目名称不能为空")
        private String name;
        private String code;
        private Integer directorid;
        private Integer sortid;

    }

}
