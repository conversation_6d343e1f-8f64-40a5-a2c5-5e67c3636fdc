package cn.savas.hub.module.client.controller.desktop.flow.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/7 18:28
 */
@Data
public class FlowNodeDirectionVO {

    @Schema(description = "流程节点标识")
    private Long alias;

    @Schema(description = "操作标识")
    private Integer operate;

    @Schema(description = "执行方向id")
    private Long id;

    @Schema(description = "对应节点id")
    private Long parentid;

    @Schema(description = "项目id")
    private Long projectid;

    @Schema(description = "绑定工程id")
    private Long hostmodelid;

    @Schema(description = "来源节点id")
    private Long sourceflownodeid;

    @Schema(description = "目标节点id")
    private Long targetflownodeid;

    @Schema(description = "备注说明")
    private String description;

    @Schema(description = "路径提交次数")
    private Integer submittedcount;

    @Schema(description = "类型 0：流程执行路径---顺序 1：流程执行路径---同时")
    private Integer style;

    @Schema(description = "状态值")
    private Integer modelstate;
}
