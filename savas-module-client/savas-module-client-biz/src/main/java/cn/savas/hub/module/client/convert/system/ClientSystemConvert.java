package cn.savas.hub.module.client.convert.system;

import cn.savas.hub.framework.common.client.enmus.ClientClassIdEnum;
import cn.savas.hub.framework.common.mapstruct.DateTimeConverter;
import cn.savas.hub.module.client.api.system.dto.ClientDataPermsPrjReqDTO;
import cn.savas.hub.module.client.api.system.dto.ClientDataPermsTreeRespDTO;
import cn.savas.hub.module.client.controller.desktop.project.vo.ClientEngineeringVO;
import cn.savas.hub.module.client.controller.desktop.system.vo.ClientNoticeReqVO;
import cn.savas.hub.module.client.controller.desktop.system.vo.ClientNoticeRespVO;
import cn.savas.hub.module.client.dal.dataobject.ClientDataPermsDO;
import cn.savas.hub.module.client.dal.dataobject.ClientNoticeDO;
import cn.savas.hub.module.client.enums.project.DataPermsEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/14 10:29
 */
@Mapper(uses = DateTimeConverter.class, imports = {ClientClassIdEnum.class, DataPermsEnum.class})
public interface ClientSystemConvert {

    ClientSystemConvert INSTANCE = Mappers.getMapper(ClientSystemConvert.class);

    @Mappings({
            @Mapping(target = "noticeId", source = "id"),
            @Mapping(target = "noticeProjectId", source = "projectid"),
            @Mapping(target = "noticeHostModel", source = "hostmodelid"),
            @Mapping(target = "noticeUserId", source = "createuserid"),
            @Mapping(target = "noticeUserName", source = "createusername"),
            @Mapping(target = "noticeDate", source = "createtime"),
            @Mapping(target = "noticeTargetUserId", source = "targetuser"),
            @Mapping(target = "noticeTargetUserName", source = "targetusername"),
            @Mapping(target = "noticeContent", source = "name"),
            @Mapping(target = "noticeRuleTag", source = "ruletag"),
            @Mapping(target = "noticeState", source = "modelstate"),
    })
    ClientNoticeDO convert(ClientNoticeReqVO.Items bean);
    List<ClientNoticeDO> convertList(List<ClientNoticeReqVO.Items> bean);

    @Mappings({
            @Mapping(target = "alias", expression = "java(ClientClassIdEnum.NOTICE.getCode())"),
            @Mapping(target = "operate", constant = "0"),
            @Mapping(target = "id", source = "noticeId"),
            @Mapping(target = "projectid", source = "noticeProjectId"),
            @Mapping(target = "hostmodelid", source = "noticeHostModel"),
            @Mapping(target = "createuserid", source = "noticeUserId"),
            @Mapping(target = "createusername", source = "noticeUserName"),
            @Mapping(target = "targetuser", source = "noticeTargetUserId"),
            @Mapping(target = "targetusername", source = "noticeTargetUserName"),
            @Mapping(target = "name", source = "noticeContent"),
            @Mapping(target = "ruletag", source = "noticeRuleTag"),
            @Mapping(target = "createtime", source = "noticeDate"),
            @Mapping(target = "modelstate", source = "noticeState"),
    })
    ClientNoticeRespVO convert2(ClientNoticeDO bean);
    List<ClientNoticeRespVO> convert2List(List<ClientNoticeDO> bean);

    @Mappings({
            @Mapping(target = "id", source = "id"),
            @Mapping(target = "pid", source = "pid"),
            @Mapping(target = "name", source = "name"),
            @Mapping(target = "type", source = "alias"),
    })
    ClientDataPermsTreeRespDTO convert3(ClientEngineeringVO bean);
    List<ClientDataPermsTreeRespDTO> convert3List(List<ClientEngineeringVO> bean);

    @Mappings({
            @Mapping(target = "modelId", source = "modelId"),
            @Mapping(target = "modelType", expression = "java(ClientClassIdEnum.getEnum(bean.getModelType()))"),
            @Mapping(target = "projectId", source = "projectId"),
            @Mapping(target = "permsType", expression = "java(DataPermsEnum.getEnum(bean.getPermsType()))"),
            @Mapping(target = "isInherited", source = "isInherited"),
    })
    ClientDataPermsDO convert4(ClientDataPermsPrjReqDTO bean);

}
