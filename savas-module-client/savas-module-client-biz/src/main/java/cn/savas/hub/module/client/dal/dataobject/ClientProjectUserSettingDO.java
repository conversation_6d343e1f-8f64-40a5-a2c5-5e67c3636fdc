package cn.savas.hub.module.client.dal.dataobject;

import cn.savas.hub.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/27 14:35
 */
@TableName(value = "client_project_user_setting")
@KeySequence("client_project_user_setting_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
public class ClientProjectUserSettingDO extends BaseDO {
    private Long id;
    private Long projectId;
    private Long userId;
    private Long paraId;
    private Long paraHostmodel;
    private Long paraParent;
    private String paraSequence;
    private String paraCategory;
    private String paraCode;
    private String paraName;
    private Integer paraSortId;
    private Integer paraEditor;
    private Integer paraStyle;
    private Integer paraValueType;
    private Long paraInt;
    private Double paraFloat;
    private String paraString;
    private String paraDescription;
    private Integer paraContext;
    private String paraRegular;
    private String paraRegularText;
    private String paraCodeItem;
    private Long paraState;
}
