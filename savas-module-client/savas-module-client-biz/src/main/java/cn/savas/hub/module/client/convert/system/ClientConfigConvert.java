package cn.savas.hub.module.client.convert.system;

import cn.savas.hub.framework.common.client.enmus.ClientClassIdEnum;
import cn.savas.hub.framework.common.mapstruct.DateTimeConverter;
import cn.savas.hub.module.client.controller.desktop.system.vo.*;
import cn.savas.hub.module.client.enums.project.DataPermsEnum;
import cn.savas.hub.module.collect.api.dto.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/14 10:29
 */
@Mapper(uses = DateTimeConverter.class, imports = {ClientClassIdEnum.class, DataPermsEnum.class})
public interface ClientConfigConvert {

    ClientConfigConvert INSTANCE = Mappers.getMapper(ClientConfigConvert.class);


    ClientPrjSettingParamDTO convert1(ClientPrjSettingParamVO bean);
    List<ClientPrjSettingParamDTO> convertList1(List<ClientPrjSettingParamVO> bean);


    ClientPrjSettingRateDTO convert2(ClientPrjSettingRateVO bean);
    List<ClientPrjSettingRateDTO> convertList2(List<ClientPrjSettingRateVO> bean);

    ClientPrjSettingEffectSetDTO convert3(ClientPrjSettingEffectSetVO bean);
    List<ClientPrjSettingEffectSetDTO> convertList3(List<ClientPrjSettingEffectSetVO> bean);


    ClientPrjSettingEffectMultiDTO convert4(ClientPrjSettingEffectMultiVO bean);
    List<ClientPrjSettingEffectMultiDTO> convertList4(List<ClientPrjSettingEffectMultiVO> bean);

    ClientPrjSettingMaterialDTO convert5(ClientPrjSettingMaterialVO bean);
    List<ClientPrjSettingMaterialDTO> convertList5(List<ClientPrjSettingMaterialVO> bean);
}
