package cn.savas.hub.module.client.controller.desktop.project.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/7 16:50
 */
@Data
public class ClientDownLoadFileReqVO {
    @Schema(description = "文件路径")
    @NotEmpty(message = "文件路径不能为空")
    private String filePath;


    @Schema(description = "工程id")
    private List<Long> engIdList;

    /**
     * 产品标识
     */
    private String product;
}
