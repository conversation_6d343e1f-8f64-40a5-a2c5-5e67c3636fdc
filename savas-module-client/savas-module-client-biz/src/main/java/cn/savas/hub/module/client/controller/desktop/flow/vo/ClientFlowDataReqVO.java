package cn.savas.hub.module.client.controller.desktop.flow.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/7 09:26
 */
@Schema(description = "管理后台 - 流程 Request VO，保存")
@Data
public class ClientFlowDataReqVO {
    @Schema(description = "流程数据",type = "array", implementation = ClientFlowDataBaseVO.class)
    private List<ClientFlowDataBaseVO> items;
}
