package cn.savas.hub.module.client.convert.flow;

import cn.savas.hub.framework.common.mapstruct.DateTimeConverter;
import cn.savas.hub.module.bpm.api.client.dto.ClientFlowDataDTO;
import cn.savas.hub.module.client.controller.desktop.flow.vo.*;
import cn.savas.hub.framework.common.client.enmus.ClientClassIdEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/30 12:00
 */
@Mapper(uses = DateTimeConverter.class, imports = {ClientClassIdEnum.class})
public interface BpmClientFlowConvert {
    BpmClientFlowConvert INSTANCE = Mappers.getMapper(BpmClientFlowConvert.class);

    @Mappings({
            @Mapping(target = "nodeId", source = "id"),
            @Mapping(target = "nodePid", source = "parentid"),
            @Mapping(target = "nodeArchiveId", source = "templateid"),
            @Mapping(target = "nodeProjectId", source = "projectid"),
            @Mapping(target = "nodeHostmodel", source = "hostmodelid"),
            @Mapping(target = "nodeName", source = "name"),
            @Mapping(target = "nodeStyle", source = "style"),
            @Mapping(target = "nodeMainId", ignore = true),
            @Mapping(target = "nodeRoleId", source = "ownerid"),
            @Mapping(target = "nodeUserId", source = "userid"),
            @Mapping(target = "nodeUserName", source = "username"),
            @Mapping(target = "nodeState", source = "modelstate"),
            @Mapping(target = "nodeSortId", source = "sortid"),
    })
    ClientFlowDataDTO.FlowNode convert1(ClientFlowDataNodeVO bean);

    @Mappings({
            @Mapping(target = "schId", source = "id"),
            @Mapping(target = "schProjectId", source = "projectid"),
            @Mapping(target = "schHostModel", source = "hostmodelid"),
            @Mapping(target = "schSortId", ignore = true),
            @Mapping(target = "schSource", source = "sourceflownodeid"),
            @Mapping(target = "schTarget", source = "targetflownodeid"),
            @Mapping(target = "schStartDate", source = "startdate"),
            @Mapping(target = "schSubmitDate", source = "submitdate"),
            @Mapping(target = "schState", source = "modelstate"),
            @Mapping(target = "schVersion", source = "version"),
            @Mapping(target = "schSourceUserId", source = "sourceuserid"),
            @Mapping(target = "schSourceUserName", source = "sourceusername"),
            @Mapping(target = "schTargetUserId", source = "targetuserid"),
            @Mapping(target = "schTargetUserName", source = "targetusername"),
    })
    ClientFlowDataDTO.FlowSchedule convert2(ClientFlowDataScheduleVO bean);

    @Mappings({
            @Mapping(target = "dirId", source = "id"),
            @Mapping(target = "dirProjectId", source = "projectid"),
            @Mapping(target = "dirHostModel", source = "hostmodelid"),
            @Mapping(target = "dirSource", source = "sourceflownodeid"),
            @Mapping(target = "dirTarget", source = "targetflownodeid"),
            @Mapping(target = "dirDescription", source = "description"),
            @Mapping(target = "dirStyle", source = "style"),
            @Mapping(target = "dirCount", source = "submittedcount"),
            @Mapping(target = "dirState", source = "modelstate"),
    })
    ClientFlowDataDTO.FlowNodeDirection convert3(ClientFlowDataNodeDirVO bean);


    List<ClientFlowDataDTO.FlowNode> convertList1(List<ClientFlowDataNodeVO> list);
    List<ClientFlowDataDTO.FlowSchedule> convertList2(List<ClientFlowDataScheduleVO> list);
    List<ClientFlowDataDTO.FlowNodeDirection> convertList3(List<ClientFlowDataNodeDirVO> list);

    List<FlowNodeVO> convertList4(List<ClientFlowDataDTO.FlowNode> list);

    @Mappings({
            @Mapping(target = "alias", expression = "java(ClientClassIdEnum.FLOWNODE.getCode())"),
            @Mapping(target = "operate", constant = "0"),
            @Mapping(target = "id", source = "nodeId"),
            @Mapping(target = "parentid", source = "nodePid"),
            @Mapping(target = "name", source = "nodeName"),
            @Mapping(target = "projectid", source = "nodeProjectId"),
            @Mapping(target = "hostmodelid", source = "nodeHostmodel"),
            @Mapping(target = "ownerid", source = "nodeRoleId"),
            @Mapping(target = "userid", source = "nodeUserId"),
            @Mapping(target = "username", source = "nodeUserName"),
            @Mapping(target = "style", source = "nodeStyle"),
            @Mapping(target = "templateid", source = "nodeArchiveId"),
            @Mapping(target = "modelstate", source = "nodeState"),
            @Mapping(target = "sortid", source = "nodeSortId"),
    })
    FlowNodeVO convert4(ClientFlowDataDTO.FlowNode bean);

    @Mappings({
            @Mapping(target = "alias", expression = "java(ClientClassIdEnum.FLOWNODEDIRECTION.getCode())"),
            @Mapping(target = "operate", constant = "0"),
            @Mapping(target = "id", source = "dirId"),
            @Mapping(target = "parentid", source = "dirSource"),
            @Mapping(target = "projectid", source = "dirProjectId"),
            @Mapping(target = "hostmodelid", source = "dirHostModel"),
            @Mapping(target = "sourceflownodeid", source = "dirSource"),
            @Mapping(target = "targetflownodeid", source = "dirTarget"),
            @Mapping(target = "description", source = "dirDescription"),
            @Mapping(target = "submittedcount", source = "dirCount"),
            @Mapping(target = "style", source = "dirStyle"),
            @Mapping(target = "modelstate", source = "dirState"),
    })
    FlowNodeDirectionVO convert5(ClientFlowDataDTO.FlowNodeDirection bean);
    List<FlowNodeDirectionVO> convertList5(List<ClientFlowDataDTO.FlowNodeDirection> list);

    @Mappings({
            @Mapping(target = "sugId", source = "id"),
            @Mapping(target = "sugProjectId", source = "projectid"),
            @Mapping(target = "sugHostmodel", source = "hostmodelid"),
            @Mapping(target = "sugModelId", source = "modelid"),
            @Mapping(target = "sugSchedule", source = "scheduleid"),
            @Mapping(target = "sugComment", source = "comment"),
            @Mapping(target = "sugContent", source = "content"),
            @Mapping(target = "sugSubmitTime", source = "submittime"),
            @Mapping(target = "sugCommentTime", source = "commenttime"),
            @Mapping(target = "sugUserId", source = "createuserid"),
            @Mapping(target = "sugUserName", source = "createusername"),
            @Mapping(target = "sugTime", source = "createtime"),
            @Mapping(target = "sugSortId", source = "sortid"),
            @Mapping(target = "sugState", source = "modelstate"),
    })
    ClientFlowDataDTO.FlowSuggestion convert6(ClientFlowSuggestVO bean);

    @Mappings({
            @Mapping(target = "alias", expression = "java(ClientClassIdEnum.SUGGESTION.getCode())"),
            @Mapping(target = "id", source = "sugId"),
            @Mapping(target = "projectid", source = "sugProjectId"),
            @Mapping(target = "hostmodelid", source = "sugHostmodel"),
            @Mapping(target = "modelid", source = "sugModelId"),
            @Mapping(target = "scheduleid", source = "sugSchedule"),
            @Mapping(target = "comment", source = "sugComment"),
            @Mapping(target = "content", source = "sugContent"),
            @Mapping(target = "submittime", source = "sugSubmitTime"),
            @Mapping(target = "commenttime", source = "sugCommentTime"),
            @Mapping(target = "createuserid", source = "sugUserId"),
            @Mapping(target = "createusername", source = "sugUserName"),
            @Mapping(target = "createtime", source = "sugTime"),
            @Mapping(target = "sortid", source = "sugSortId"),
            @Mapping(target = "modelstate", source = "sugState"),
    })
    FlowSuggestionRespVO convert7(ClientFlowDataDTO.FlowSuggestion bean);

    @Mappings({
            @Mapping(target = "alias", expression = "java(ClientClassIdEnum.FLOWSCHEDULE.getCode())"),
            @Mapping(target = "operate", constant = "0"),
            @Mapping(target = "id", source = "schId"),
            @Mapping(target = "projectid", source = "schProjectId"),
            @Mapping(target = "hostmodelid", source = "schHostModel"),
            @Mapping(target = "sourceflownodeid", source = "schSource"),
            @Mapping(target = "targetflownodeid", source = "schTarget"),
            @Mapping(target = "startdate", source = "schStartDate"),
            @Mapping(target = "submitdate", source = "schSubmitDate"),
            @Mapping(target = "version", source = "schVersion"),
            @Mapping(target = "sourceuserid", source = "schSourceUserId"),
            @Mapping(target = "sourceusername", source = "schSourceUserName"),
            @Mapping(target = "targetuserid", source = "schTargetUserId"),
            @Mapping(target = "targetusername", source = "schTargetUserName"),
            @Mapping(target = "modelstate", source = "schState"),

    })
    FlowScheduleRespVO convert8(ClientFlowDataDTO.FlowSchedule bean);

    List<FlowScheduleRespVO> convertList8(List<ClientFlowDataDTO.FlowSchedule> beans);
}
