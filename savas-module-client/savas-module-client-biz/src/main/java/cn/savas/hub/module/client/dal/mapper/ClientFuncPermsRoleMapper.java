package cn.savas.hub.module.client.dal.mapper;

import cn.savas.hub.framework.common.util.collection.CollectionUtils;
import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.module.client.dal.dataobject.ClientFuncPermsRuleDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/1 17:02
 */
@Mapper
public interface ClientFuncPermsRoleMapper extends BaseMapperX<ClientFuncPermsRuleDO> {
    default List<ClientFuncPermsRuleDO> getRoleClientPermissions(Long roleId){
        return selectList(ClientFuncPermsRuleDO::getRoleId, roleId);
    }

    default void deleteByRoleId(Long roleId){
        delete(ClientFuncPermsRuleDO::getRoleId, roleId);
    }

    default void insertBatch(Long roleId, List<Long> menuIds){
        insertBatch(CollectionUtils.convertList(menuIds, menuId -> {
            ClientFuncPermsRuleDO entity = new ClientFuncPermsRuleDO();
            entity.setRoleId(roleId);
            entity.setFuncId(menuId);
            return entity;
        }));
    }
}
