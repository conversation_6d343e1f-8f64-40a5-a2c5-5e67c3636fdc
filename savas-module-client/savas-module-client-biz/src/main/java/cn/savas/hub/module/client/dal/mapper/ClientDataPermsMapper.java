package cn.savas.hub.module.client.dal.mapper;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.module.client.dal.dataobject.ClientDataPermsDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/1 17:02
 */
@Mapper
public interface ClientDataPermsMapper extends BaseMapperX<ClientDataPermsDO> {
    default List<ClientDataPermsDO> selectByUserId(Long loginUserId){
        return selectList(ClientDataPermsDO::getUserId, loginUserId);
    }
}
