package cn.savas.hub.module.client.api.project;

import cn.savas.hub.framework.common.client.enmus.ClientClassIdEnum;
import cn.savas.hub.framework.common.pojo.PageResult;
import cn.savas.hub.framework.common.util.object.BeanUtils;
import cn.savas.hub.module.client.api.project.dto.ClientEngineeringRespDTO;
import cn.savas.hub.module.client.api.project.dto.ClientProjectReqDTO;
import cn.savas.hub.module.client.api.project.dto.ClientProjectRespDTO;
import cn.savas.hub.module.client.dal.dataobject.ClientEngineeringDO;
import cn.savas.hub.module.client.dal.dataobject.ClientProjectDO;
import cn.savas.hub.module.client.dal.mapper.ClientEngineeringMapper;
import cn.savas.hub.module.client.dal.mapper.ClientProjectMapper;
import cn.savas.hub.module.client.service.system.ClientPermsService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/11/19 11:49
 */
@Slf4j
@Service
public class ClientProjectApiImpl implements ClientProjectApi {
    @Resource
    private ClientEngineeringMapper clientEngineeringMapper;
    @Resource
    private ClientProjectMapper clientProjectMapper;
    @Resource
    private ClientPermsService clientPermsService;
    @Override
    public List<ClientEngineeringRespDTO> getEngineeringIdList(Long prjId) {
        List<ClientEngineeringDO> doList = clientEngineeringMapper.selectList(
                new LambdaQueryWrapper<ClientEngineeringDO>()
                        .select(ClientEngineeringDO::getEngId)
                        .eq(ClientEngineeringDO::getEngProjectId, prjId)
        );
        return BeanUtils.toBean(doList, ClientEngineeringRespDTO.class);
    }

    @Override
    public PageResult<ClientProjectRespDTO> getProjectPageList(ClientProjectReqDTO req) {
        if (req.getDirectorId() == null) {
            return new PageResult<>();
        }
        Set<Long> authorizedProjectIds = clientPermsService.getAuthorizedProjectIds();
        if (CollectionUtils.isEmpty(authorizedProjectIds)) {
            return new PageResult<>();
        }
        PageResult<ClientProjectDO> pageResult = clientProjectMapper.selectPage(req,
                new LambdaQueryWrapper<ClientProjectDO>()
                        .in(ClientProjectDO::getProjectId, authorizedProjectIds)
        );
        return BeanUtils.toBean(pageResult, ClientProjectRespDTO.class);
    }

    @Override
    public List<ClientProjectRespDTO> getProjectList(ClientProjectReqDTO req) {
        if (req.getDirectorId() == null) {
            return new ArrayList<>();
        }
        List<ClientProjectDO> doList = clientProjectMapper.selectList(
                new LambdaQueryWrapper<ClientProjectDO>()
                        .eq(req.getDirectorId() != null, ClientProjectDO::getDirectorId, req.getDirectorId())
        );
        return BeanUtils.toBean(doList, ClientProjectRespDTO.class);
    }

    @Override
    public List<ClientEngineeringRespDTO> getBpmProjectByEngId(Collection<Long> engId) {
        if (engId == null || engId.isEmpty()) {
            return new ArrayList<>();
        }
        List<ClientEngineeringDO> doList = clientEngineeringMapper.selectList(
                new LambdaQueryWrapper<ClientEngineeringDO>()
                        .select(ClientEngineeringDO::getEngId, ClientEngineeringDO::getEngProjectId)
                        .in(ClientEngineeringDO::getEngId, engId)
        );
        return BeanUtils.toBean(doList, ClientEngineeringRespDTO.class);
    }

    @Override
    public List<ClientEngineeringRespDTO> getDeviceEngByPrjId(Collection<Long> prjId) {
        if (prjId == null || prjId.isEmpty()) {
            return new ArrayList<>();
        }
        List<ClientEngineeringDO> doList = clientEngineeringMapper.selectList(
                new LambdaQueryWrapper<ClientEngineeringDO>()
                        .select(ClientEngineeringDO::getEngId,
                                ClientEngineeringDO::getEngProjectId,
                                ClientEngineeringDO::getEngName,
                                ClientEngineeringDO::getEngCode,
                                ClientEngineeringDO::getEngParameters,
                                ClientEngineeringDO::getEngClass
                        )
                        .in(ClientEngineeringDO::getEngProjectId, prjId)
        );
        return BeanUtils.toBean(doList, ClientEngineeringRespDTO.class);
    }

    @Override
    public List<ClientEngineeringRespDTO> getDeviceEngByName(String name, Collection<Long> projectIds) {
        if (name == null || name.isEmpty() || CollectionUtils.isEmpty(projectIds)) {
            return new ArrayList<>();
        }
        List<ClientEngineeringDO> doList = clientEngineeringMapper.selectList(
                new LambdaQueryWrapper<ClientEngineeringDO>()
                        .select(ClientEngineeringDO::getEngId,
                                ClientEngineeringDO::getEngProjectId,
                                ClientEngineeringDO::getEngName
                        )
                        .in(ClientEngineeringDO::getEngProjectId, projectIds)
                        .eq(ClientEngineeringDO::getEngClass, ClientClassIdEnum.UNITENG.getCode())
                        .like(ClientEngineeringDO::getEngName, name)
        );
        return BeanUtils.toBean(doList, ClientEngineeringRespDTO.class);
    }

    @Override
    public List<ClientProjectRespDTO> getProjectByIdList(Collection<Long> projectIds) {
        if (CollectionUtils.isEmpty(projectIds)) {
            return new ArrayList<>();
        }
        List<ClientProjectDO> doList = clientProjectMapper.selectList(
                new LambdaQueryWrapper<ClientProjectDO>()
                        .select(ClientProjectDO::getProjectId)
                        .in(ClientProjectDO::getProjectId, projectIds)
        );
        return BeanUtils.toBean(doList, ClientProjectRespDTO.class);
    }
}
