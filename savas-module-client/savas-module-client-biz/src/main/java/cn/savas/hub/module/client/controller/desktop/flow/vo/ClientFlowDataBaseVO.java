package cn.savas.hub.module.client.controller.desktop.flow.vo;

import cn.savas.hub.module.client.controller.desktop.flow.vo.resolver.FlowAliasTypeIdResolver;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.annotation.JsonTypeIdResolver;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/8 11:26
 */
@Data
@JsonTypeInfo(use = JsonTypeInfo.Id.CUSTOM, property = "alias", visible = true)
@JsonTypeIdResolver(FlowAliasTypeIdResolver.class)
public class ClientFlowDataBaseVO {
    @Schema(description = "数据类型")
    private Long alias;

    @Schema(description = "操作类型（0：没有变化，1：插入，2：修改，3：删除）")
    private Integer operate;

    @Schema(description = "数据id")
    private Long id;

    @Schema(description = "项目ID")
    private Long projectid;

    @Schema(description = "工程id")
    private Long hostmodelid;

    @Schema(description = "")
    private Long modelstate;
}
