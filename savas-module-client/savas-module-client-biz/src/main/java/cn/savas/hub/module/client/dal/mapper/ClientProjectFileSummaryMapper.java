package cn.savas.hub.module.client.dal.mapper;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.savas.hub.module.client.dal.dataobject.ClientProjectFileSummaryDO;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @date 2024/11/5 16:31
 */
@Mapper
public interface ClientProjectFileSummaryMapper extends BaseMapperX<ClientProjectFileSummaryDO> {

    default ClientProjectFileSummaryDO selectActiveByPrjId(Long prjId){
        return selectOne(new LambdaQueryWrapperX<ClientProjectFileSummaryDO>()
                .eq(ClientProjectFileSummaryDO::getProjectId, prjId)
                .eq(ClientProjectFileSummaryDO::getIsActive, true)
        );
    }

    default void deleteByPrjId(Long projectId){
        delete(new LambdaQueryWrapperX<ClientProjectFileSummaryDO>().eq(ClientProjectFileSummaryDO::getProjectId, projectId));
    }

    default ClientProjectFileSummaryDO selectByPath(String filePath){
        return selectOne(new LambdaQueryWrapperX<ClientProjectFileSummaryDO>().eq(ClientProjectFileSummaryDO::getPath, filePath));
    }

    default void updateActiveFalse(Long projectId){
        update(new LambdaUpdateWrapper<ClientProjectFileSummaryDO>()
                .eq(ClientProjectFileSummaryDO::getProjectId, projectId)
                .eq(ClientProjectFileSummaryDO::getIsActive, true)
                .set(ClientProjectFileSummaryDO::getIsActive, false));

    }

    default ClientProjectFileSummaryDO selectByVersion(Long engProjectId, Integer version){
        return selectOne(new LambdaQueryWrapperX<ClientProjectFileSummaryDO>()
                .eq(ClientProjectFileSummaryDO::getProjectId, engProjectId)
                .eq(ClientProjectFileSummaryDO::getVersion, version));
    }
}
