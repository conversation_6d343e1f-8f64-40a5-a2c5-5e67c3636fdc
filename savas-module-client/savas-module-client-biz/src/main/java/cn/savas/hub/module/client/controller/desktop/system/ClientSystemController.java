package cn.savas.hub.module.client.controller.desktop.system;

import cn.savas.hub.framework.common.pojo.ClientItemsResult;
import cn.savas.hub.framework.common.pojo.ClientResult;
import cn.savas.hub.framework.common.util.io.FileTypeUtils;
import cn.savas.hub.module.client.controller.desktop.system.vo.ClientFuncPermsRespVO;
import cn.savas.hub.module.client.controller.desktop.system.vo.ClientNoticeReqVO;
import cn.savas.hub.module.client.controller.desktop.system.vo.ClientNoticeRespVO;
import cn.savas.hub.module.client.controller.desktop.system.vo.UserDataRoleRespVO;
import cn.savas.hub.module.client.convert.system.UserDataRoleConvert;
import cn.savas.hub.module.client.service.system.ClientPermsService;
import cn.savas.hub.module.client.service.system.ClientSystemService;
import cn.savas.hub.module.infra.api.file.FileApi;
import cn.savas.hub.module.system.api.permission.RoleApi;
import cn.savas.hub.module.system.api.permission.dto.UserDataRoleRespDTO;
import cn.savas.hub.module.system.enums.permission.RoleCodeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/22 17:21
 */
@Tag(name = "协同客户端 - 系统")
@RestController
@RequestMapping("/client/system")
@Slf4j
public class ClientSystemController {
    @Resource
    private RoleApi roleApi;
    @Resource
    private ClientPermsService clientPermsService;
    @Resource
    private ClientSystemService clientSystemService;
    @Resource
    private FileApi fileApi;

    @GetMapping("/getUsersByRole")
    @Operation(summary = "根据角色获取用户")
    @Parameter(name = "RoleId", description = "角色id", required = true)
    public ClientItemsResult<List<UserDataRoleRespVO>> getUsersByRole(@RequestParam("RoleId") String roleId) {
        List<UserDataRoleRespDTO> roleUsers = roleApi.getRoleUsers(Long.parseLong(roleId));
        return ClientItemsResult.success(UserDataRoleConvert.INSTANCE.convertList(roleUsers));
    }

    @GetMapping("/getLeaderRoleUsers")
    @Operation(summary = "获取负责人角色用户")
    public ClientItemsResult<List<UserDataRoleRespVO>> getLeaderRoleUsers() {
        List<UserDataRoleRespDTO> roleUsers = roleApi.getRoleUsersByRoleCode(RoleCodeEnum.CLIENT_PROJECT_LEADER.getCode());
        return ClientItemsResult.success(UserDataRoleConvert.INSTANCE.convertList(roleUsers));
    }

    @GetMapping("/getFuncPermissions")
    @Operation(summary = "获取功能权限")
    public ClientResult<ClientFuncPermsRespVO> getFuncPermissions() {
        return ClientResult.success(clientPermsService.getFuncPermissions());
    }

    @PostMapping("/sendNotice")
    @Operation(summary = "客户端发送用户通知")
    public ClientResult<Map> sendNotice(@Valid @RequestBody ClientNoticeReqVO notice) {
        clientSystemService.sendNotice(notice);
        return ClientResult.success(new HashMap());
    }

    @GetMapping("/getNotice")
    @Operation(summary = "客户端获取用户通知")
    public ClientItemsResult<List<ClientNoticeRespVO>> getNotice() {
        return ClientItemsResult.success(clientSystemService.getNotice());
    }

    @GetMapping("/downloadFile")
    @Operation(summary = "文件下载")
    @Parameter(name = "path", description = "文件路径", required = true)
    public void downloadFile(HttpServletResponse response,
                             @RequestParam("path") String path) throws Exception {
        byte[] fileContent = fileApi.getFileContent(path);
        if (fileContent == null) {
            log.warn("[clientDownloadFile][path({}) 文件不存在]", path);
            response.setStatus(HttpStatus.NOT_FOUND.value());
            return;
        }
        FileTypeUtils.writeAttachment(response, path, fileContent);
    }

//
//    @PostMapping("/addFuncPermissions")
//    @Operation(summary = "新增功能权限")
//    public ClientResult<Boolean> addFuncPermissions(@RequestBody ClientFuncPermsReqVO perms) {
//        clientPermsService.addFuncPermissions(perms);
//        return ClientResult.success(true);
//    }
//
//    @PostMapping("/updateFuncPermissions")
//    @Operation(summary = "修改功能权限")
//    public ClientResult<Boolean> updateFuncPermissions(@RequestBody ClientFuncPermsReqVO perms) {
//        clientPermsService.updateFuncPermissions(perms);
//        return ClientResult.success(true);
//    }
//
//    @PostMapping("/deleteFuncPermissions")
//    @Operation(summary = "删除功能权限")
//    @Parameter(name = "ID", description = "功能权限id", required = true)
//    public ClientResult<Boolean> deleteFuncPermissions(@RequestParam("ID") String id) {
//        clientPermsService.deleteFuncPermissions(id);
//        return ClientResult.success(true);
//    }

//    @GetMapping("/getDataPermissions")
//    @Operation(summary = "获取数据权限")
//    public ClientResult<List<ClientDataPermsRespVO>> getDataPermissions() {
//        return ClientResult.success(clientPermsService.getDataPermissions());
//    }
//
//    @PostMapping("/addDataPermissions")
//    @Operation(summary = "新增数据权限")
//    public ClientResult<Boolean> addDataPermissions(@RequestBody ClientDataPermsReqVO perms) {
//        clientPermsService.addDataPermissions(perms);
//        return ClientResult.success(true);
//    }
//
//    @PostMapping("/updateDataPermissions")
//    @Operation(summary = "修改数据权限")
//    public ClientResult<Boolean> updateDataPermissions(@RequestBody ClientDataPermsReqVO perms) {
//        clientPermsService.updateDataPermissions(perms);
//        return ClientResult.success(true);
//    }
//
//    @PostMapping("/deleteDataPermissions")
//    @Operation(summary = "删除数据权限")
//    @Parameter(name = "ID", description = "数据权限id", required = true)
//    public ClientResult<Boolean> deleteDataPermissions(@RequestParam("ID") String id) {
//        clientPermsService.deleteDataPermissions(id);
//        return ClientResult.success(true);
//    }
}
