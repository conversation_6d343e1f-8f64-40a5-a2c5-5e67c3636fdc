package cn.savas.hub.module.client.controller.desktop.price;


import cn.savas.hub.framework.common.pojo.ClientItemsResult;
import cn.savas.hub.framework.common.pojo.ClientResult;
import cn.savas.hub.module.client.controller.desktop.price.vo.ClientPriceCustomRespVO;
import cn.savas.hub.module.client.service.price.ClientPriceCustomService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static cn.savas.hub.framework.common.pojo.ClientResult.success;

/**
 * <AUTHOR>
 * @date 2025/4/8 11:09
 */
@Tag(name = "协同前端 - 自定义价格库")
@RestController
@RequestMapping("/client/price")
@Validated
public class ClientPriceCustomController {
    @Resource
    private ClientPriceCustomService clientPriceCustomService;

    // 获取自定义标准价格库列表
    @GetMapping("/standard/list")
    @Operation(summary = "获取自定义标准价格库列表")
    public ClientItemsResult<List<ClientPriceCustomRespVO>> getStandardCustomPriceList() {
        return ClientItemsResult.success(clientPriceCustomService.getStandardCustomPriceList());
    }

    // 获取自定义电缆价格库列表
    @GetMapping("/cable/list")
    @Operation(summary = "获取自定义电缆价格库列表")
    public ClientItemsResult<List<ClientPriceCustomRespVO>> getCableCustomPriceList() {
        return ClientItemsResult.success(clientPriceCustomService.getCableCustomPriceList());
    }
}
