package cn.savas.hub.module.client.dal.mapper;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.savas.hub.module.client.dal.dataobject.ClientProjectSettingDO;
import cn.savas.hub.module.client.dal.dataobject.ClientProjectUserSettingDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/11/27 14:45
 */
@Mapper
public interface ClientProjectUserSettingMapper extends BaseMapperX<ClientProjectUserSettingDO> {

    default void deleteOldData(Long projectId, Set<Long> paraIdSet, Long userId){
        delete(new LambdaQueryWrapper<ClientProjectUserSettingDO>()
                .eq(ClientProjectUserSettingDO::getProjectId, projectId)
                .eq(ClientProjectUserSettingDO::getUserId, userId)
                .in(ClientProjectUserSettingDO::getParaId, paraIdSet)
        );
    }

    default void deleteByPrjId(Long prjId){
        delete(new LambdaQueryWrapper<ClientProjectUserSettingDO>()
                .eq(ClientProjectUserSettingDO::getProjectId, prjId)
        );
    }
}
