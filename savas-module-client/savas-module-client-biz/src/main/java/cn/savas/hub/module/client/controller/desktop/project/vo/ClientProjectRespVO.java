package cn.savas.hub.module.client.controller.desktop.project.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/6 09:35
 */
@Data
public class ClientProjectRespVO {
    private Long alias;
    private Integer operate;
    private String edition;
    private Long createuserid;
    private Integer submittedversion;
    private String createusername;
    private String createtime;
    private String ruletag;
    private String expression;
    private String factors;
    private Integer style;
    private String phase;
    private Integer compiledversion;
    private Long modelstate;
    private String directorname;

    @Schema(description = "项目id")
    private Long id;

    private Long projectid;

    private Long originalid;

    @Schema(description = "项目名称")
    private String name;

    private String code;
    private Integer directorid;
    private Integer sortid;

    @Schema(description = "流程状态(编制中,已完成)")
    private String description;

    @Schema(description = "项目组子集")
    private List<ClientProjectRespVO> items;
}
