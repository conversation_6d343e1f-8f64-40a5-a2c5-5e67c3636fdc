package cn.savas.hub.module.client.controller.desktop.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/4/15 11:13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ClientNoticeRespVO {
    @Schema(description = "标识")
    private Long alias;
    @Schema(description = "操作类型")
    private Integer operate;
    @Schema(description = "创建人id")
    private Long createuserid;
    @Schema(description = "状态")
    private Integer modelstate;
    @Schema(description = "目标用户名")
    private String targetusername;
    @Schema(description = "创建时间")
    private LocalDateTime createtime;
    @Schema(description = "创建人用户名")
    private String createusername;
    @Schema(description = "项目id")
    private Long projectid;
    @Schema(description = "名称")
    private String name;
    @Schema(description = "规则标签")
    private String ruletag;
    @Schema(description = "工程id")
    private Long hostmodelid;
    @Schema(description = "id")
    private Long id;
    @Schema(description = "目标用户id")
    private Long targetuser;
}
