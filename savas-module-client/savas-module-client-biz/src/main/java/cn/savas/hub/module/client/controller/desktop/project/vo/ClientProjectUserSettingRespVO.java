package cn.savas.hub.module.client.controller.desktop.project.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/3 16:32
 */
@Data
public class ClientProjectUserSettingRespVO {
    private Long alias;
    private Integer operate;
    private Long id;
    private Long parentid;
    private Long hostmodelid;
    private String code;
    private String name;
    private Integer sortid;
    private Integer editor; // 编辑器类型
    private Integer style; // 样式
    private Integer datatype; // 数据类型
    private Long intvalue; // 整型值
    private Double floatvalue; // 浮点型值
    private String strvalue; // 字符串值
    private String validateregular; // 验证正则
    private String description; // 描述
    private Integer context; // 上下文
    private String codeitem; // 代码项
    private String validatetext; // 验证文本
    private Long modelstate; // 模型状态
}
