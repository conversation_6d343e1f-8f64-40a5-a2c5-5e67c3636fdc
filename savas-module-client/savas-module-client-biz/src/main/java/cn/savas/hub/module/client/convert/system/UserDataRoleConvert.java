package cn.savas.hub.module.client.convert.system;

import cn.savas.hub.module.client.controller.desktop.system.vo.UserDataRoleRespVO;
import cn.savas.hub.framework.common.client.enmus.ClientClassIdEnum;
import cn.savas.hub.module.system.api.permission.dto.UserDataRoleRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;


@Mapper(imports = {ClientClassIdEnum.class})
public interface UserDataRoleConvert {

    UserDataRoleConvert INSTANCE = Mappers.getMapper(UserDataRoleConvert.class);

    @Mappings({
            @Mapping(target = "alias", expression = "java(ClientClassIdEnum.USER.getCode())"),
            @Mapping(target = "operate", constant = "0"),
            @Mapping(target = "id", source = "userId"),
            @Mapping(target = "name", source = "userName"),
            @Mapping(target = "loginname", source = "userLoginName"),
    })
    UserDataRoleRespVO convert(UserDataRoleRespDTO bean);
    List<UserDataRoleRespVO> convertList(List<UserDataRoleRespDTO> bean);

}
