package cn.savas.hub.module.client.controller.desktop.flow.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/7 18:16
 */
@Data
public class ClientFlowTemplateRespVO {
    @Schema(description = "流程模板标识")
    private Long alias;

    @Schema(description = "操作标识")
    private Integer operate;

    @Schema(description = "流程模板id")
    private Long id;

    @Schema(description = "流程模板名称")
    private String name;

    @Schema(description = "创建人id")
    private Long createuserid;

    @Schema(description = "创建人名称")
    private String createusername;

    @Schema(description = "创建时间")
    private LocalDateTime createtime;

    @Schema(description = "状态")
    private Integer modelstate;

    @Schema(description = "顺序号")
    private Integer sortid;

    @Schema(description = "流程图")
    private String flowchart;

    @Schema(description = "流程节点", type = "array", implementation = TempNodeVO.class)
    private List<TempNodeVO> items;
}
