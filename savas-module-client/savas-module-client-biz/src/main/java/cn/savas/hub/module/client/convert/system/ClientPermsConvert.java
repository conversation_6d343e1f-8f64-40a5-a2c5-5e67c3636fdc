package cn.savas.hub.module.client.convert.system;

import cn.savas.hub.framework.common.client.enmus.ClientClassIdEnum;
import cn.savas.hub.module.client.api.system.dto.ClientFuncPermsReqDTO;
import cn.savas.hub.module.client.controller.desktop.system.vo.ClientDataPermsReqVO;
import cn.savas.hub.module.client.controller.desktop.system.vo.ClientFuncPermsRespVO;
import cn.savas.hub.module.client.dal.dataobject.ClientDataPermsDO;
import cn.savas.hub.module.client.dal.dataobject.ClientFuncPermsDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/14 10:29
 */
@Mapper(imports = {ClientClassIdEnum.class})
public interface ClientPermsConvert {

    ClientPermsConvert INSTANCE = Mappers.getMapper(ClientPermsConvert.class);

    @Mappings({
            @Mapping(target = "alias", expression = "java(ClientClassIdEnum.SYSTEMFUNCTION.getCode())"),
            @Mapping(target = "operate", constant = "0"),
            @Mapping(target = "parentid", constant = "0L"),
            @Mapping(target = "target", source = "permission"),
            @Mapping(target = "order", constant = "1"),
            @Mapping(target = "style", source = "type"),
            @Mapping(target = "sorid", source = "sort"),
            @Mapping(target = "group", constant = "0"),
            @Mapping(target = "imageindex", constant = "0"),
    })
    ClientFuncPermsRespVO.Items convert2(ClientFuncPermsDO bean);
    List<ClientFuncPermsRespVO.Items> convertList2(List<ClientFuncPermsDO> bean);


    ClientFuncPermsDO convert3(ClientFuncPermsReqDTO bean);

    ClientDataPermsDO convert5(ClientDataPermsReqVO.ClientDataPermsReqsVO bean);
}
