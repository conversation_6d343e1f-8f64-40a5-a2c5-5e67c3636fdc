package cn.savas.hub.module.client.dal.dataobject;

import cn.savas.hub.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/11/5 16:22
 */
@TableName("client_project_file")
@KeySequence("client_project_file_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
public class ClientProjectFileDO extends BaseDO {
    private Long id;
    private Long projectId;
    private String name;
    private String path;
    private String url;
    private Integer size;
    private Integer version;
    private Boolean isActive;
}
