package cn.savas.hub.module.client.controller.desktop.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/20 10:09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ClientSettingRespVO {
    @Schema(description = "配置参数")
    private List<Map<String, Object>> items;
    @Schema(description = "配置版本")
    private Integer version;
}
