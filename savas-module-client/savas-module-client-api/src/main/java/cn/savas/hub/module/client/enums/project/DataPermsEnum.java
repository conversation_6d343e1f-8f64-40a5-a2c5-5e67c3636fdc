package cn.savas.hub.module.client.enums.project;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/4/18 11:28
 */
@Getter
public enum DataPermsEnum {
    // 只读
    READ("read", "只读"),
    // 读写
    WRITE("write", "读写"),
    // 管理
    ADMIN("admin", "管理"),;

    private String code;
    private String name;
    DataPermsEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    public static DataPermsEnum getByCode(String code) {
        for (DataPermsEnum value : DataPermsEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static DataPermsEnum getEnum(String name) {
        for (DataPermsEnum value : DataPermsEnum.values()) {
            if (value.getName().equals(name)) {
                return value;
            }
        }
        return null;
    }
}
