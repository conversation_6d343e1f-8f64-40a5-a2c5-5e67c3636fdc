package cn.savas.hub.module.client.api.system.dto;

import cn.savas.hub.framework.common.client.enmus.ClientClassIdEnum;
import cn.savas.hub.module.client.enums.project.DataPermsEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/6 15:29
 */
@Data
public class ClientDataPermsRespDTO {
    private Long id;
    private Long userId;
    /**
     * 权限数据ID
     */
    private Long modelId;
    /**
     * 权限数据类型
     */
    private ClientClassIdEnum modelType;
    /**
     * 项目ID
     */
    private Long projectId;
    /**
     * 权限类型：只读、读写、管理
     */
    private DataPermsEnum permsType;
    /**
     * 是否继承自父节点
     */
    private Integer isInherited;
}
