package cn.savas.hub.module.client.enums.system;

import lombok.Getter;

/**
 * 发送客户端socket的消息类型
 * <AUTHOR>
 * @date 2024/12/5 10:37
 */
@Getter
public enum CSocketMessageTypeEnum {
    FORCE_LOGOUT("force-logout", "强制下线"),
    DELETE_PROJECT("delete-project", "删除项目"),
    DICT_CHANGE("dict-change", "字典配置变更"),
    RATE_SETTING_CHANGE("rate-setting-change", "费率配置变更"),
    PROJECT_SETTING_CHANGE("project-setting-change", "项目配置变更"),
    MATERIAL_SETTING_CHANGE("material-setting-change", "材料系数配置变更"),
    TODO_TASK_CHANGE("todo-task-change", "待办事项变更"),
    ENGINEERING_FEE_SETTING_CHANGE("effect-fee-set-setting-change", "装置费用降效设置变更")
    ;



    private String code;
    private String message;

    CSocketMessageTypeEnum(String code, String name) {
        this.code = code;
        this.message = name;
    }

}
