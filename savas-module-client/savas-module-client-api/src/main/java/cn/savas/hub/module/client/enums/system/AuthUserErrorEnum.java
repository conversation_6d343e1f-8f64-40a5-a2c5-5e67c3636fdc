package cn.savas.hub.module.client.enums.system;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/12/4 13:57
 */
@Getter
public enum AuthUserErrorEnum {
    LOGIN_SUCCESS(0, "登录成功"),
    USER_NOT_FOUND_OR_DISABLED(1, "用户不存在或被禁用"),
    USER_DISABLED(2, "用户被禁止登录"),
    USER_LOGGED_IN_ELSEWHERE(3, "用户已在其他地方登录"),
    PASSWORD_ERROR(4, "密码错误"),
    PASSWORD_EXPIRED(5, "密码已过期"),
    PASSWORD_WILL_EXPIRE(6, "密码即将过期"),
    ;

    private final Integer code;
    private final String message;

    AuthUserErrorEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static String getMessage(Integer code) {
        for (AuthUserErrorEnum item : AuthUserErrorEnum.values()) {
            if (item.getCode().equals(code)) {
                return item.getMessage();
            }
        }
        return null;
    }
}
