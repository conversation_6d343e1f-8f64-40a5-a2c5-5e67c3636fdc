package cn.savas.hub.module.client.enums.project;

import cn.savas.hub.module.client.enums.system.CSocketMessageTypeEnum;
import lombok.Getter;

import static cn.savas.hub.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.savas.hub.module.client.enums.ErrorCodeConstants.PROJECT_SETTING_TYPE_NOT_FOUND;

/**
 * <AUTHOR>
 * @date 2024/11/27 15:07
 */
@Getter
public enum ProjectSettingEnum {
    RATE_SETTING("rate_setting", "费率设置", CSocketMessageTypeEnum.RATE_SETTING_CHANGE),
    PROJECT_SETTING("project_setting", "项目设置", CSocketMessageTypeEnum.PROJECT_SETTING_CHANGE),
    MATERIAL_SETTING("material_setting", "主材系数设置", CSocketMessageTypeEnum.MATERIAL_SETTING_CHANGE),
    ENGINEERING_FEE_SETTING("effect_fee_set", "装置费用降效设置", CSocketMessageTypeEnum.ENGINEERING_FEE_SETTING_CHANGE)
    ;

    private final String code;
    private final String name;
    private final CSocketMessageTypeEnum socketMessageTypeEnum;

    ProjectSettingEnum(String code, String name, CSocketMessageTypeEnum socketMessageTypeEnum) {
        this.code = code;
        this.name = name;
        this.socketMessageTypeEnum = socketMessageTypeEnum;
    }
    public static ProjectSettingEnum getByCode(String code) {
        for (ProjectSettingEnum value : ProjectSettingEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw exception(PROJECT_SETTING_TYPE_NOT_FOUND, code);
    }
}
