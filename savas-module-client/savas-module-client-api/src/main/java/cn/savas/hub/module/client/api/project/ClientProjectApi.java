package cn.savas.hub.module.client.api.project;

import cn.savas.hub.framework.common.pojo.PageResult;
import cn.savas.hub.module.client.api.project.dto.ClientEngineeringRespDTO;
import cn.savas.hub.module.client.api.project.dto.ClientProjectReqDTO;
import cn.savas.hub.module.client.api.project.dto.ClientProjectRespDTO;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/11/19 11:49
 */
public interface ClientProjectApi {
    /**
     * 获取工程ID数据
     */
    List<ClientEngineeringRespDTO> getEngineeringIdList(Long prjId);

    /**
     * 获取项目分页数据
     */
    PageResult<ClientProjectRespDTO> getProjectPageList(ClientProjectReqDTO req);
    List<ClientProjectRespDTO> getProjectList(ClientProjectReqDTO req);
    /**
     * 获取工程ID数据
     */
    List<ClientEngineeringRespDTO> getBpmProjectByEngId(Collection<Long> engId);

    /**
     * 根据项目ID获取工程数据
     */
    List<ClientEngineeringRespDTO> getDeviceEngByPrjId(Collection<Long> prjId);

    /**
     * 根据名称模糊查询工程数据
     */
    List<ClientEngineeringRespDTO> getDeviceEngByName(String name, Collection<Long> projectIds);

    List<ClientProjectRespDTO> getProjectByIdList(Collection<Long> projectIds);

}
