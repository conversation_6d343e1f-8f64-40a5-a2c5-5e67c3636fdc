package cn.savas.hub.module.client.enums;

import cn.savas.hub.framework.common.exception.ErrorCode;

/**
 * client 错误码枚举类
 *
 * client 系统，使用 1-001-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== 参数配置 1-020-000-000 ==========
    ErrorCode PROJECT_NOT_EXISTS = new ErrorCode(1_020_000_001, "项目不存在");
    ErrorCode PROJECT_FILE_NOT_EXISTS = new ErrorCode(1_020_000_002, "项目文件不存在");
    ErrorCode PROJECT_ID_INVALID = new ErrorCode(1_020_000_003, "项目ID(HostModel)不统一");
    ErrorCode PROJECT_DATASOURCE_SWITCH_FAIL = new ErrorCode(1_020_000_004, "项目数据源切换失败:{}");
    ErrorCode PROJECT_FILE_NO_PERMISSION = new ErrorCode(1_020_000_005, "没有权限下载该文件");
    ErrorCode PROJECT_NOT_BIND_BPM = new ErrorCode(1_020_000_006, "项目未绑定流程模板");
    ErrorCode SERIALIZE_FAIL = new ErrorCode(1_020_000_007, "序列化返回值失败");
    ErrorCode PROJECT_FILE_MERGE_FAIL = new ErrorCode(1_020_000_008, "项目文件合并失败");
    ErrorCode UNSUPPORTED_ALIAS_TYPE = new ErrorCode(1_020_000_009, "不受支持的alias类型");
    ErrorCode PROJECT_NO_PERMISSION = new ErrorCode(1_020_000_010, "没有权限访问该项目");
    ErrorCode PROJECT_SUMMARY_FILE_NOT_EXISTS = new ErrorCode(1_020_000_011, "项目汇总文件不存在:{}");
    ErrorCode PROJECT_FILE_CALCULATE_FAIL = new ErrorCode(1_020_000_012, "项目文件计算失败:{}");
    ErrorCode PROJECT_NO_PERMISSION_LOCK_FLOW = new ErrorCode(1_020_000_013, "没有权限锁定流程");
    ErrorCode PROJECT_NO_PERMISSION_UNLOCK_FLOW = new ErrorCode(1_020_000_014, "没有权限解锁流程");
    ErrorCode PROJECT_FLOW_LOCKED = new ErrorCode(1_020_000_015, "已被锁定:{}");
    ErrorCode PROJECT_FLOW_FORCE_MERGE_ENGID_EMPTY = new ErrorCode(1_020_000_016, "强制合并工程ID不能为空");
    ErrorCode PROJECT_SUMMARY_FILE_NO_PERMISSION = new ErrorCode(1_020_000_017, "没有权限获取汇总文件");
    ErrorCode PROJECT_FLOW_LOCK_HOSTMODELIDS_EMPTY = new ErrorCode(1_020_000_018, "锁定流程时，工程ID集合不能为空");
    ErrorCode PROJECT_SETTING_TYPE_NOT_FOUND = new ErrorCode(1_020_000_019, "未找到适合的设置类型:{}，请检查设置类型是否正确");

}
