package cn.savas.hub.module.collect.service;

import cn.savas.hub.module.collect.dal.dataobject.DictCostCalculator;
import cn.savas.hub.module.collect.dal.dataobject.PrjCostcalculator;
import cn.savas.hub.module.collect.dal.handler.dto.InterpolationFeeRuleVO;
import cn.savas.hub.module.collect.dal.mapper.DictCostCalculatorMapper;
import cn.savas.hub.module.collect.dal.mapper.PrjCostcalculatorMapper;
import cn.savas.hub.framework.common.util.io.XStreamUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class PrjCostcalculatorServiceImpl implements PrjCostcalculatorService {
    @Resource
    private DictCostCalculatorMapper dictCostCalculatorMapper;
    @Resource
    private PrjCostcalculatorMapper prjCostcalculatorMapper;

    @Override
    public List<PrjCostcalculator> getCulculatorList() {
        List<PrjCostcalculator> list = new ArrayList<>();
        List<DictCostCalculator> dictCalList = dictCostCalculatorMapper.selectList();
        List<PrjCostcalculator> prjCalValue = prjCostcalculatorMapper.selectList();
        Map<String, PrjCostcalculator> valueMap = prjCalValue.stream().collect(Collectors.toMap(PrjCostcalculator::getCalcName, p -> p));
        for (DictCostCalculator dict : dictCalList) {
            PrjCostcalculator prj = valueMap.get(dict.getCalcName());
            if (prj != null) {
                InterpolationFeeRuleVO.Factor prjFactor = this.getFactorVoByXml(prj.getXml());
                dict.getCalcConfig().setFactor(prjFactor);
                prj.setCalcConfig(dict.getCalcConfig());
                list.add(prj);
            }
        }
        return list;
    }

    private InterpolationFeeRuleVO.Factor getFactorVoByXml(String xml) {
        try {
            return XStreamUtil.XML2Bean(xml.trim(), InterpolationFeeRuleVO.Factor.class);
        } catch (Exception e) {
            InterpolationFeeRuleVO interVO = XStreamUtil.XML2Bean(xml.trim(), InterpolationFeeRuleVO.class);
            return interVO.getFactor();
        }
    }
}
