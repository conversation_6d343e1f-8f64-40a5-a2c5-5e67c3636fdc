package cn.savas.hub.module.collect.service;

import cn.savas.hub.module.collect.api.ErrorCodeConstants;
import cn.savas.hub.module.collect.api.dto.ClientMergeProjectFileDTO;
import cn.savas.hub.module.collect.dal.dataobject.FeeEngineering;
import cn.savas.hub.module.collect.dal.mapper.SqliteCollectMapper;
import cn.savas.hub.module.collect.enums.EngineerCollectTableEnum;
import cn.savas.hub.module.collect.framework.annotations.ChangeSqliteDB;
import cn.savas.hub.module.collect.manager.ReduceProjectManager;
import cn.savas.hub.module.collect.manager.ReduceProjectManager2;
import cn.savas.hub.module.collect.mq.msg.CalculateRedisStreamMessage;
import cn.savas.hub.module.collect.strategy.ProductMergeStrategyResolver;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

import static cn.savas.hub.framework.common.exception.util.ServiceExceptionUtil.exception;

@Log4j2
@Service
public class SqliteCollectServiceImpl implements SqliteCollectService {

    @Resource
    private SqliteCollectMapper sqliteCollectMapper;
    @Resource
    private ReduceProjectManager2 reduceProjectManager2;
    @Resource
    private PackageService packageService;
    @Resource
    private ProductMergeStrategyResolver strategyResolver;

    @ChangeSqliteDB(value = "#msg.mainDB")
    @Override
    public void calculateSqliteData(CalculateRedisStreamMessage msg) {
        // 计算检验检测费
        strategyResolver.resolve(msg.getProduct()).processCalculateFee(msg.getWbsIds());
        //重新计算上层数据//暂不处理
        ArrayList<FeeEngineering> oldList = new ArrayList<>();
        try {
            reduceProjectManager2.reSummaryProjectCostToMain(msg.getWbsIds(), oldList, msg.getProduct());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("计算失败，参数: {}", msg);
            throw new RuntimeException(e.getMessage());
        }
        // 打包文件
        log.info("计算完成，为projectId打包文件: {}", msg.getProjectId());
        packageService.packageFileAndRecord(msg.getProjectId(), msg.getMainFilePath(), msg.getMainDB(), msg.getProjectFileId());
    }


    @ChangeSqliteDB(value = "#mainDB")
    @Override
    public void mergeProjectData(ClientMergeProjectFileDTO req, String mainDB, String slaveDB) {
        // 1.参数验证
        if (StringUtils.isBlank(req.getMainPath()) || StringUtils.isBlank(mainDB) || StringUtils.isBlank(slaveDB)) {
            throw new IllegalArgumentException("文件路径或数据库名称不能为空");
        }
        if (CollectionUtils.isEmpty(req.getWbsIds())) {
            throw new IllegalArgumentException("WBS列表不能为null或空");
        }

        File slaveDBFile = new File(slaveDB);
        if (!slaveDBFile.exists()) {
            throw exception(ErrorCodeConstants.SLAVE_NOT_EXISTS);
        }
        // 2.由于有删除信息：需要获取原始信息
        List<FeeEngineering> oldEngList = reduceProjectManager2.getEnginnerAndProjectList();
        // 3.添加从库链接
        sqliteCollectMapper.attachDatabase(slaveDB);
        // 4.合并数据-统一处理
        for (Long wbsId : req.getWbsIds()) {
            for (EngineerCollectTableEnum table : EngineerCollectTableEnum.values()) {
                //清空
                sqliteCollectMapper.deleteOldDataByTemplate(table.getTable(), table.getCol(), wbsId);
                //合并
                sqliteCollectMapper.mergeDataFromSlaveDB(table.getTable(), table.getCol(), wbsId);
            }
        }
        // 4.1.合并数据-工程属性单独处理
        sqliteCollectMapper.deletePrjParameter(req.getWbsIds());
        sqliteCollectMapper.mergePrjParameter(req.getWbsIds());
        // 4.2.合并数据-产品单独处理
        strategyResolver.resolve(req.getProduct()).processMergeFee(req);
        //是否增加工程版本
        if (req.isIncreaseEngVersion()) {
            //增加工程版本
            for (Long wbsId : req.getWbsIds()) {
                sqliteCollectMapper.increaseEngVersion(wbsId);
            }
            //增加项目版本
            sqliteCollectMapper.increaseProjectVersion(req.getProjectId());
        }
        log.info("开始计算数据: {}", req.getWbsIds());
        // 计算检验检测费
        strategyResolver.resolve(req.getProduct()).processCalculateFee(req.getWbsIds());
        // 重新计算上层数据
        reduceProjectManager2.reSummaryProjectCostToMain(req.getWbsIds(), oldEngList, req.getProduct());
        // 打包文件
        log.info("开始打包，projectId: {}", req.getProjectId());
        packageService.packageFileAndRecord(req.getProjectId(), req.getMainPath(), mainDB, req.getProjectFileId());
    }
}
