package cn.savas.hub.module.collect.service;

import cn.savas.hub.module.collect.api.dto.CalculateReqDTO;
import cn.savas.hub.module.collect.api.dto.ClientMergeProjectFileDTO;

public interface CollectDataService {

    /**
     * 合并项目文件
     * @param build
     * @throws Exception
     */
    void mergeProjectFile(ClientMergeProjectFileDTO reqVO) throws Exception;

    void initFeeMap(String dbUri);

    /**
     * 计算项目数据
     */
    void calculateProjectData(CalculateReqDTO reqDTO) throws Exception;
}
