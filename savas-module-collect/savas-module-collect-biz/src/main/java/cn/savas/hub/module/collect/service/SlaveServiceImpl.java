package cn.savas.hub.module.collect.service;

import cn.savas.hub.module.collect.dal.dataobject.FeeEngineering;
import cn.savas.hub.module.collect.dal.mapper.PrjEngineeringMapper;
import cn.savas.hub.module.collect.framework.annotations.ChangeSqliteDB;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Log4j2
@Service
public class SlaveServiceImpl implements SlaveService {

    @Resource
    private PrjEngineeringMapper prjEngineeringMapper;

    @ChangeSqliteDB(value = "#dbUri")
    @Override
    public List<FeeEngineering> getSlaveEngineerById(String dbUri,List<Long> engIdList) {

        return prjEngineeringMapper.selectList(new LambdaQueryWrapper<FeeEngineering>().in(FeeEngineering::getEngId, engIdList));
    }

}
