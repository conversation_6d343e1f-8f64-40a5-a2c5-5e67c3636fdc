package cn.savas.hub.module.collect.service;

import cn.savas.hub.module.collect.api.dto.ClientMergeProjectFileDTO;
import cn.savas.hub.module.collect.mq.msg.CalculateRedisStreamMessage;

import java.io.UnsupportedEncodingException;

public interface SqliteCollectService {

    /**
     * 计算数据
     * @param message
     * @throws UnsupportedEncodingException
     */
    void calculateSqliteData(CalculateRedisStreamMessage message) throws UnsupportedEncodingException;

    /**
     * 合并结构数据
     * @param req 合并请求
     * @param mainDB  解压的公共库 db
     * @param slaveDB 解压的个人库 db
     */
    void mergeProjectData(ClientMergeProjectFileDTO req, String mainDB, String slaveDB);
}
