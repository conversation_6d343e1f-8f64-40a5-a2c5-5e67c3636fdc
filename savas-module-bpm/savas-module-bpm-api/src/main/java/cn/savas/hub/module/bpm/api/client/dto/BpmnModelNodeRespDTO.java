package cn.savas.hub.module.bpm.api.client.dto;

import cn.savas.hub.module.bpm.api.definition.dto.BpmnModelSequenceRespDTO;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/22 18:13
 */
@Data
public class BpmnModelNodeRespDTO {

    private Long id;
    private Long parentid;
    private String nodeName;
    private Integer style;// 流程节点类型 0---开始节点；1---中间节点；2---完成节点
    private Integer clientDefinitionId;
    private Map<String, Object> attributes;
    private List<BpmnModelSequenceRespDTO> directions;// 连线信息
    private Integer nodeState; //  modelstate and 64 = 64 执行中, modelstate and 256 = 256 已完成, modelstate and 512 = 512 终止
    private Long nodeUserId; // 执行人ID
    private Long nodeArchiveId; // Node模板ID
    private Long nodeHostmodel; // 关联的业务ModelID
}
