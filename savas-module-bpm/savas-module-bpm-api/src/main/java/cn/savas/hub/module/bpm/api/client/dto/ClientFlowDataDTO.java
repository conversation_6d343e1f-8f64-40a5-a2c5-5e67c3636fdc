package cn.savas.hub.module.bpm.api.client.dto;

import cn.savas.hub.framework.common.client.enmus.ClientOperateEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/2/8 13:56
 */
@Data
public class ClientFlowDataDTO {
    private Map<ClientOperateEnum, FlowDataContainer> flowData = new EnumMap<>(ClientOperateEnum.class);

    public ClientFlowDataDTO() {
        // 初始化所有操作类型的容器
        Arrays.stream(ClientOperateEnum.values())
                .forEach(type -> flowData.put(type, new FlowDataContainer()));
    }

    @Data
    public static class FlowNode{
        private Long nodeId;
        private Long nodePid;
        /**
         * Node模板ID
         */
        private Long nodeArchiveId;
        /**
         * 方案ID
         */
        private Long nodeProjectId;
        /**
         * 关联的业务ModelID
         */
        private Long nodeHostmodel;
        /**
         * 业务流程名字
         */
        private String nodeName;
        /**
         * 0 START 1 MAIN 2 ASSIST 3 ASSIST_SYNC 4 FINISHED
         */
        private Integer nodeStyle;
        /**
         * 主流程ID
         */
        private Long nodeMainId;
        /**
         * 角色ID
         */
        private Long nodeRoleId;
        /**
         * 执行人ID
         */
        private Long nodeUserId;
        /**
         * 执行人名称
         */
        private String nodeUserName;
        /**
         * 提交次数
         */
        private Integer nodeCount;
        /**
         * 1 执行中 2 已提交 4 已经完成
         */
        private Long nodeState;
        private Integer nodeSortId;
    }
    @Data
    public static class FlowSchedule{
        private Long schId;
        /**
         * 方案ID
         */
        private Long schProjectId;
        /**
         * 业务ModelID
         */
        private Long schHostModel;
        /**
         * 顺序
         */
        private Integer schSortId;
        /**
         * 从哪个 node_ID
         */
        private Long schSource;
        /**
         * 目标 node_ID
         */
        private Long schTarget;
        private LocalDateTime schStartDate;
        private LocalDateTime schSubmitDate;
        private Long schState;
        private Integer schVersion;
        /**
         * 来源流程执行人ID
         */
        private Long schSourceUserId;
        /**
         * 来源流程执行人名称
         */
        private String schSourceUserName;
        /**
         * 目标流程执行人ID
         */
        private Long schTargetUserId;
        /**
         * 目标流程执行人名称
         */
        private String schTargetUserName;
    }
    @Data
    public static class FlowNodeDirection{
        private Long dirId;
        /**
         * 方案ID
         */
        private Long dirProjectId;
        /**
         * 业务ModelID
         */
        private Long dirHostModel;
        /**
         * 从哪个 node_ID
         */
        private Long dirSource;
        /**
         * 目标 node_ID
         */
        private Long dirTarget;

        private String dirDescription;

        private Integer dirStyle;

        private Integer dirCount;

        private Long dirState;
    }

    @Data
    public static class FlowSuggestion{
        /**
         *
         */
        private Long sugId;
        /**
         * 方案ID
         */
        private Long sugProjectId;
        /**
         * 工程ID， 具有流程Node的业务项
         */
        private Long sugHostmodel;
        /**
         * 业务Model ID
         */
        private Long sugModelId;
        /**
         * 建议节点
         */
        private Long sugSchedule;
        /**
         * 回复内容
         */
        private String sugComment;
        /**
         * 建议内容
         */
        private String sugContent;
        /**
         * 提交时间
         */
        private LocalDateTime sugSubmitTime;
        /**
         * 回复时间
         */
        private LocalDateTime sugCommentTime;
        /**
         * 创建人ID
         */
        private Long sugUserId;
        /**
         * 创建人名称
         */
        private String sugUserName;
        /**
         * 创建时间
         */
        private LocalDateTime sugTime;
        /**
         * 顺序
         */
        private Integer sugSortId;
        private Long sugState;
    }


    @Data
    public static class FlowDataContainer {
        private List<FlowNode> nodes = new ArrayList<>();
        private List<FlowSchedule> schedules = new ArrayList<>();
        private List<FlowNodeDirection> directions = new ArrayList<>();
        private List<FlowSuggestion> suggestions = new ArrayList<>();
    }
}
