package cn.savas.hub.module.bpm.api.client.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/16 17:34
 */
@Data
public class ClientFlowNodeDTO {

    private Long nodeId;

    /**
     *
     */
    private Long nodePid;

    /**
     * Node模板ID
     */
    private Long nodeArchiveId;

    /**
     * 方案ID
     */
    private Long nodeProjectId;

    /**
     * 关联的业务ModelID
     */
    private Long nodeHostmodel;

    /**
     * 业务流程名字
     */
    private String nodeName;

    /**
     * 0 START 1 MAIN 2 ASSIST 3 ASSIST_SYNC 4 FINISHED
     */
    private Integer nodeStyle;

    /**
     * 主流程ID
     */
    private Long nodeMainId;

    /**
     * 角色ID
     */
    private Long nodeRoleId;

    /**
     * 执行人ID
     */
    private Long nodeUserId;

    /**
     * 执行人名称
     */
    private String nodeUserName;
    /**
     * 1 执行中 2 已提交 4 已经完成
     */
    private Long nodeState;

    /**
     *
     */
    private Integer nodeSortId;
}
