package cn.savas.hub.module.bpm.api.definition.dto;

import cn.savas.hub.module.bpm.api.client.dto.BpmnModelNodeRespDTO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/22 18:13
 */
@Data
public class BpmnModelRespDTO {
    private Integer id;
    private String name;
    private Long createuserid;
    private String createusername;
    private LocalDateTime createtime;
    private Long sortid;
    private List<BpmnModelNodeRespDTO> nodes;
}
