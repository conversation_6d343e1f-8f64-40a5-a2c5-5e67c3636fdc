package cn.savas.hub.module.bpm.api.client;

import cn.savas.hub.module.bpm.api.client.dto.*;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/10/23 16:30
 */
public interface BpmClientApi {

    /**
     * 批量获取流程任务节点，根据流程
     */
    List<BpmClientHistoricReqDTO> getTaskListByProcessInstanceIds(List<String> processInstanceIdList);

    /**
     * 获取编校审流程实例信息
     */
    List<BpmOaProofreadingDTO> getProofreadingList(Long originalId);

    /**
     * 获取指定人相关流程项目(不包含终止)
     * @param loginUserId
     */
    Set<Long> getTaskProjectIds(Long loginUserId);

    /**
     * 删除编校审审核（会删除流程数据）
     * @param prjId
     */
    List<BpmOaProofreadingDTO> delPrjProofreading(Long prjId);

    /**
     * 获取流程模版信息
     */
    ClientFlowTemplateDTO getFlowTemplate();

    /**
     * 保存流程数据
     */
    void saveFlowData(ClientFlowDataDTO flowDataDTO);

    /**
     * 获取流程数据
     */
    ClientFlowDataDTO.FlowDataContainer getFlowData(Long originalid);

    /**
     * 删除流程数据
     */
    void delFlowDataByPrjId(Long prjId);

    /**
     * 获取流程节点数据
     * @param flownodeid
     * @return
     */
    List<ClientFlowNodeDTO> getFlowNodeById(Collection<Long> flownodeids);

    /**
     * 绑定汇总文件版本
     * @param flowScheduleId
     * @param sumFileVersion
     */
    void bindSumFileVersion(Long flowScheduleId, Integer sumFileVersion);

    /**
     * 获取项目的流程状态
     * @param originalid 项目原始ID
     */
    List<ClientFlowStateDTO> getPrjFlowState(List<Long> originalid);
}
