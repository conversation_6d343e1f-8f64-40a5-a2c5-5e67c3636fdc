package cn.savas.hub.module.bpm.api.client.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/7 17:48
 */
@Data
@AllArgsConstructor
public class ClientFlowTemplateDTO {
    private List<FlowTemplate> flowTemplateList;
    private List<FlowNode> flowNodeList;
    private List<FlowDirection> flowDirectionList;
    @Data
    public static class FlowTemplate{
        private Long workId;
        private String workName;
        private byte[] workFlowChart;
        private Long workUserId;
        private String workUserName;
        private Date workDate;
        private Long workState;
    }

    @Data
    public static class FlowDirection{
        private Long dirId;
        /**
         * 业务ModelID
         */
        private Long dirWorkFlow;
        /**
         * 从哪个 node_ID
         */
        private Long dirSource;
        /**
         * 目标 node_ID
         */
        private Long dirTarget;
        private String dirDescription;
        private Integer dirStyle;
    }

    @Data
    public static class FlowNode{
        private Long nodeId;
        private Long nodePid;
        /**
         * 流程模板D
         */
        private Long nodeWorkFlow;
        /**
         * 业务流程名字
         */
        private String nodeName;
        /**
         * 0 START 1 MAIN 2 ASSIST 3 ASSIST_SYNC 4 FINISHED
         */
        private Integer nodeStyle;
        /**
         * 主流程ID
         */
        private Long nodeMainId;
        /**
         * 角色ID
         */
        private Long nodeRoleId;
        private Integer nodeSortId;
    }
}
