package cn.savas.hub.module.bpm.enums.client;

/**
 * <AUTHOR>
 * @date 2025/2/11 15:57
 */
public enum ClientWorkStateEnum {
    // 未开始 0
    NOT_STARTED(0L, "未开始"),
    // 执行中 64
    RUNNING(64L, "执行中"),
    // 已完成 256
    COMPLETED(256L, "已完成"),
    // 终止 512
    TERMINATED(512L, "终止"),
    ;

    private final Long state;
    private final String description;

    ClientWorkStateEnum(Long state, String description) {
        this.state = state;
        this.description = description;
    }

    public Long getState() {
        return state;
    }

    public String getDescription() {
        return description;
    }
}
