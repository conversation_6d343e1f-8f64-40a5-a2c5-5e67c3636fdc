package cn.savas.hub.module.bpm.api.definition.dto;

import cn.savas.hub.module.bpm.enums.definition.BpmModelFormTypeEnum;
import cn.savas.hub.module.bpm.enums.definition.BpmModelTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/23 09:27
 */
@Data
public class BpmProcessDefinitionInfoDTO {
    /**
     * 编号
     */
    private Long id;
    /**
     * 流程定义的编号
     *
     */
    private String processDefinitionId;
    /**
     * 为客户端生成的流程定义编号
     *
     */
    private Integer clientDefinitionId;
    /**
     * 流程模型的编号
     *
     */
    private String modelId;
    /**
     * 流程模型的类型
     *
     * 枚举 {@link BpmModelTypeEnum}
     */
    private Integer modelType;

    /**
     * 图标
     */
    private String icon;
    /**
     * 描述
     */
    private String description;

    /**
     * 表单类型
     *
     * 枚举 {@link BpmModelFormTypeEnum}
     */
    private Integer formType;
    /**
     * 动态表单编号
     *
     * 在表单类型为 {@link BpmModelFormTypeEnum#NORMAL} 时
     *
     */
    private Long formId;
    /**
     * 表单的配置
     *
     * 在表单类型为 {@link BpmModelFormTypeEnum#NORMAL} 时
     *
     */
    private String formConf;
    /**
     * 表单项的数组
     *
     * 在表单类型为 {@link BpmModelFormTypeEnum#NORMAL} 时
     *
     */
    private List<String> formFields;
    /**
     * 自定义表单的提交路径，使用 Vue 的路由地址
     *
     * 在表单类型为 {@link BpmModelFormTypeEnum#CUSTOM} 时
     */
    private String formCustomCreatePath;
    /**
     * 自定义表单的查看路径，使用 Vue 的路由地址
     *
     * 在表单类型为 {@link BpmModelFormTypeEnum#CUSTOM} 时
     */
    private String formCustomViewPath;

    /**
     * SIMPLE 设计器模型数据 json 格式
     *
     * 目的：当使用仿钉钉设计器时。流程模型发布的时候，需要保存流程模型设计器的快照数据。
     */
    private String simpleModel;
    /**
     * 是否可见
     *
     * 目的：如果 false 不可见，则不展示在“发起流程”的列表里
     */
    private Boolean visible;

    /**
     * 可发起用户编号数组
     *
     *
     * 如果为空，则表示“全部可以发起”！
     *
     * 它和 {@link #visible} 的区别在于：
     * 1. {@link #visible} 只是决定是否可见。即使不可见，还是可以发起
     * 2. startUserIds 决定某个用户是否可以发起。如果该用户不可发起，则他也是不可见的
     */
    private List<Long> startUserIds;

    /**
     * 可管理用户编号数组
     *
     */
    private List<Long> managerUserIds;
}
