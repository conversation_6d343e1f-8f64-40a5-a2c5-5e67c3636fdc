package cn.savas.hub.module.bpm.enums.oa;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/10/23 16:07
 */
@AllArgsConstructor
@Getter
public enum ClientBpmModelEnum {
    // 节点id
    START("开始"),
    BZ("编制"),
    JH("校核"),
    SH("审核"),
    HG("合稿"),
    SD("审定"),
    END("结束"),

    ;
    private String nodeName;

    public static ClientBpmModelEnum getByNodeName(String nodeName) {
        for (ClientBpmModelEnum value : ClientBpmModelEnum.values()) {
            if (value.getNodeName().equals(nodeName)) {
                return value;
            }
        }
        return null;
    }
}
