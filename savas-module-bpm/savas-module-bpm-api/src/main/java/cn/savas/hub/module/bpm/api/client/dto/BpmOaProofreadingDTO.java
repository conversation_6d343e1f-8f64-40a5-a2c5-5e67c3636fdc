package cn.savas.hub.module.bpm.api.client.dto;

import cn.savas.hub.module.bpm.enums.task.BpmTaskStatusEnum;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/4 11:10
 */
@Data
public class BpmOaProofreadingDTO {
    private Long id;
    private Long originalId;
    private Long engId;
    private String processInstanceId;
    private Map<String, String> actVariable;
    /**
     * 审批结果
     *
     * 枚举 {@link BpmTaskStatusEnum}
     */
    private Integer status;
}
