<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.savas.hub</groupId>
        <artifactId>savas-module-bpm</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>savas-module-bpm-biz</artifactId>

    <name>${project.artifactId}</name>
    <description>
        bpm 包下，业务流程管理（Business Process Management），我们放工作流的功能，基于 Flowable 6 版本实现。
        例如说：流程定义、表单配置、审核中心（我的申请、我的待办、我的已办）等等
    </description>
    <dependencies>
        <!-- API 相关 -->
        <dependency>
            <groupId>cn.savas.hub</groupId>
            <artifactId>savas-spring-boot-starter-websocket</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.savas.hub</groupId>
            <artifactId>savas-module-client-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.savas.hub</groupId>
            <artifactId>savas-module-bpm-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.savas.hub</groupId>
            <artifactId>savas-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>cn.savas.hub</groupId>
            <artifactId>savas-spring-boot-starter-biz-data-permission</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.savas.hub</groupId>
            <artifactId>savas-spring-boot-starter-biz-tenant</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>cn.savas.hub</groupId>
            <artifactId>savas-spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.savas.hub</groupId>
            <artifactId>savas-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>cn.savas.hub</groupId>
            <artifactId>savas-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>cn.savas.hub</groupId>
            <artifactId>savas-spring-boot-starter-excel</artifactId>
        </dependency>

        <!-- Flowable 工作流相关 -->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-spring-boot-starter-process</artifactId>
        </dependency>
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-spring-boot-starter-actuator</artifactId>
        </dependency>
    </dependencies>
</project>
