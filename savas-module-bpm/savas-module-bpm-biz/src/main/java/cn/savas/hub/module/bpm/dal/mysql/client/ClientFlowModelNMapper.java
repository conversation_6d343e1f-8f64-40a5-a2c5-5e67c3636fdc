package cn.savas.hub.module.bpm.dal.mysql.client;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.module.bpm.dal.dataobject.client.ClientWorkflowNodeDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @date 2025/2/7 18:02
 */
@Mapper
public interface ClientFlowModelNMapper extends BaseMapperX<ClientWorkflowNodeDO>{
    default void deleteByWorkflowId(Long workflowId){
        delete(ClientWorkflowNodeDO::getNodeWorkFlow, workflowId);
    }
}
