package cn.savas.hub.module.bpm.dal.mysql.client;

import cn.savas.hub.framework.common.pojo.PageResult;
import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.savas.hub.module.bpm.controller.admin.client.vo.ClientFlowModelPageReqVO;
import cn.savas.hub.module.bpm.dal.dataobject.client.ClientWorkflowDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @date 2025/2/7 15:01
 */
@Mapper
public interface ClientFlowModelMapper extends BaseMapperX<ClientWorkflowDO> {
    default PageResult<ClientWorkflowDO> selectPage(ClientFlowModelPageReqVO reqVO){
        return selectPage(reqVO, new LambdaQueryWrapperX<>());
    }
}
