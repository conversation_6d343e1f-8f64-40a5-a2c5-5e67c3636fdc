package cn.savas.hub.module.bpm.service.client;

import cn.savas.hub.framework.common.pojo.PageResult;
import cn.savas.hub.module.bpm.controller.admin.client.vo.ClientFlowModelDetailRespVO;
import cn.savas.hub.module.bpm.controller.admin.client.vo.ClientFlowModelPageReqVO;
import cn.savas.hub.module.bpm.controller.admin.client.vo.ClientFlowModelReqVO;
import cn.savas.hub.module.bpm.controller.admin.client.vo.ClientFlowModelUpdateReqVO;
import cn.savas.hub.module.bpm.dal.dataobject.client.ClientWorkflowDO;

/**
 * <AUTHOR>
 * @date 2025/2/7 13:59
 */
public interface ClientFlowModelService {
    void createFlowModel(ClientFlowModelReqVO saveFlowTemplateReqVO);

    void updateFlowModel(ClientFlowModelUpdateReqVO updateFlowModelReqVO);

    PageResult<ClientWorkflowDO> getFlowModelPage(ClientFlowModelPageReqVO reqVO);

    ClientFlowModelDetailRespVO getFlowModelDetail(Long workId);

    void delFlowModel(Long id);
}
