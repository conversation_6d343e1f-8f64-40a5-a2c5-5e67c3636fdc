package cn.savas.hub.module.bpm.dal.mysql.client;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.module.bpm.dal.dataobject.client.ClientFlowNodeDirectionDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/7 15:01
 */
@Mapper
public interface ClientFlowNodeDirMapper extends BaseMapperX<ClientFlowNodeDirectionDO> {


    default List<ClientFlowNodeDirectionDO> selectByPrjId(Long originalid){
        return selectList(ClientFlowNodeDirectionDO::getDirProjectId, originalid);
    }

    /**
     * 排序
     */
    List<ClientFlowNodeDirectionDO> selectByPrjIdAndSort(@Param("originalid") Long originalid);
}
