package cn.savas.hub.module.bpm.dal.mysql.client;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.module.bpm.dal.dataobject.client.ClientSuggestionDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/20 16:04
 */
@Mapper
public interface ClientSuggestionMapper extends BaseMapperX<ClientSuggestionDO> {
    default List<ClientSuggestionDO> selectByPrjId(Long originalid){
        return selectList(ClientSuggestionDO::getSugProjectId, originalid);
    }
}
