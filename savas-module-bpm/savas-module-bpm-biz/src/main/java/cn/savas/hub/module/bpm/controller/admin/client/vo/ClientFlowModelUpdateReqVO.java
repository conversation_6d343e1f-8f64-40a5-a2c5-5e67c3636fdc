package cn.savas.hub.module.bpm.controller.admin.client.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/2/7 13:53
 */
@Data
public class ClientFlowModelUpdateReqVO {
    @Schema(description = "流程模板ID")
    @NotNull(message = "模板ID不能为空")
    private Long id;

    @Schema(description = "流程模板名称")
    private String name;

    @Schema(description = "起否启用(1.启用 0.禁用)")
    private Integer enable;

    @Schema(description = "流程图")
    private String bpmnXml;

    @Schema(description = "流程图SVG")
    private String bpmnSvg;
}
