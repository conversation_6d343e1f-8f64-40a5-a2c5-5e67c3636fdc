package cn.savas.hub.module.bpm.dal.mysql.client;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.module.bpm.dal.dataobject.client.ClientFlowScheduleDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/7 15:01
 */
@Mapper
public interface ClientFlowScheduleMapper extends BaseMapperX<ClientFlowScheduleDO> {

    default List<ClientFlowScheduleDO> selectByPrjId(Long originalid){
        return selectList(ClientFlowScheduleDO::getSchProjectId, originalid);
    }
}
