package cn.savas.hub.module.bpm.service.oa.listener;

import cn.savas.hub.module.bpm.event.BpmProcessInstanceStatusEvent;
import cn.savas.hub.module.bpm.event.BpmProcessInstanceStatusEventListener;
import cn.savas.hub.module.bpm.service.oa.BpmClientService;
import cn.savas.hub.module.bpm.service.oa.BpmClientServiceImpl;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * OA
 *
 * <AUTHOR>
 */
@Component
public class BpmOAClientStatusListener extends BpmProcessInstanceStatusEventListener {

    @Resource
    private BpmClientService clientService;

    @Override
    protected String getProcessDefinitionKey() {
        return BpmClientServiceImpl.PROCESS_KEY;
    }

    @Override
    protected void onEvent(BpmProcessInstanceStatusEvent event) {
        clientService.updateStatus(Long.parseLong(event.getBusinessKey()), event.getStatus());
    }

}
