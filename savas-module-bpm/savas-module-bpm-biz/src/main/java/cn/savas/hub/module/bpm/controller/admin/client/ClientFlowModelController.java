package cn.savas.hub.module.bpm.controller.admin.client;

import cn.savas.hub.framework.common.pojo.CommonResult;
import cn.savas.hub.framework.common.pojo.PageResult;
import cn.savas.hub.module.bpm.controller.admin.client.vo.*;
import cn.savas.hub.module.bpm.convert.client.ClientFlowModelConvert;
import cn.savas.hub.module.bpm.dal.dataobject.client.ClientWorkflowDO;
import cn.savas.hub.module.bpm.service.client.ClientFlowModelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.savas.hub.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @date 2024/10/22 16:06
 */
@Tag(name = "客户端 - 流程模板")
@RestController
@RequestMapping("/bpm/client/flowModel")
public class ClientFlowModelController {
    @Resource
    private ClientFlowModelService flowModelService;

    @PostMapping("/createFlowModel")
    @Operation(summary = "创建流程模板")
    public CommonResult<Boolean> createFlowModel(@Valid @RequestBody ClientFlowModelReqVO saveFlowTemplateReqVO) {
        flowModelService.createFlowModel(saveFlowTemplateReqVO);
        return success(true);
    }

    @PostMapping("/updateFlowModel")
    @Operation(summary = "更新流程模板")
    public CommonResult<Boolean> updateFlowModel(@Valid @RequestBody ClientFlowModelUpdateReqVO updateFlowModelReqVO) {
        flowModelService.updateFlowModel(updateFlowModelReqVO);
        return success(true);
    }

    @GetMapping("/getFlowModelPage")
    @Operation(summary = "获取流程模板分页")
    public CommonResult<PageResult<ClientFlowModelRespVO>> getFlowModelPage(@Valid ClientFlowModelPageReqVO reqVO) {
        PageResult<ClientWorkflowDO> flowModelPage = flowModelService.getFlowModelPage(reqVO);
        List<ClientFlowModelRespVO> list = ClientFlowModelConvert.INSTANCE.convertList5(flowModelPage.getList());
        return success(new PageResult<>(list, flowModelPage.getTotal()));
    }

    @GetMapping("/getFlowModelDetail")
    @Operation(summary = "获取流程模板详情")
    public CommonResult<ClientFlowModelDetailRespVO> getFlowModelDetail(@RequestParam Long id) {
        return success(flowModelService.getFlowModelDetail(id));
    }

    @DeleteMapping("/delFlowModel")
    @Operation(summary = "删除流程模板")
    public CommonResult<Boolean> delFlowModel(@RequestParam Long id){
        flowModelService.delFlowModel(id);
        return success(true);
    }

}
