package cn.savas.hub.module.bpm.dal.mysql.oa;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.module.bpm.dal.dataobject.oa.BpmOaProofreadingDO;
import cn.savas.hub.module.bpm.enums.task.BpmTaskStatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/22 14:18
 */
@Mapper
public interface BpmOaProofreadingMapper extends BaseMapperX<BpmOaProofreadingDO> {
    default List<BpmOaProofreadingDO> selectByInsId(String processInstanceId){
        return selectList(BpmOaProofreadingDO::getProcessInstanceId, processInstanceId);
    }

    default List<BpmOaProofreadingDO> selectByPrjIdOnRun(Long originalId){
        return selectList(new LambdaQueryWrapper<BpmOaProofreadingDO>()
                .eq(BpmOaProofreadingDO::getOriginalId, originalId)
                .eq(BpmOaProofreadingDO::getStatus, BpmTaskStatusEnum.RUNNING.getStatus())
        );
    }

    default BpmOaProofreadingDO selectOneByEngIdOnStatus(Long engId){
        return selectOne(new LambdaQueryWrapper<BpmOaProofreadingDO>()
                .eq(BpmOaProofreadingDO::getEngId, engId)
                .ne(BpmOaProofreadingDO::getStatus, BpmTaskStatusEnum.CANCEL.getStatus())
        );
    }

    default List<BpmOaProofreadingDO> selectOneByPrjIdOnStatus(Long originalId){
        return selectList(new LambdaQueryWrapper<BpmOaProofreadingDO>()
                .eq(BpmOaProofreadingDO::getOriginalId, originalId)
                .ne(BpmOaProofreadingDO::getStatus, BpmTaskStatusEnum.CANCEL.getStatus())
        );
    }

    default List<BpmOaProofreadingDO> selectByPrjId(Long originalId){
        return selectList(new LambdaQueryWrapper<BpmOaProofreadingDO>()
                .eq(BpmOaProofreadingDO::getOriginalId, originalId)
        );
    }

    default List<BpmOaProofreadingDO> selectByPrjEngsId(Long originalId, Collection<Long> engIdList){
        if (CollectionUtils.isEmpty(engIdList)) {
            return Collections.emptyList();
        }
        return selectList(new LambdaQueryWrapper<BpmOaProofreadingDO>()
                .eq(BpmOaProofreadingDO::getOriginalId, originalId)
                .in(BpmOaProofreadingDO::getEngId, engIdList)
        );
    }

}
