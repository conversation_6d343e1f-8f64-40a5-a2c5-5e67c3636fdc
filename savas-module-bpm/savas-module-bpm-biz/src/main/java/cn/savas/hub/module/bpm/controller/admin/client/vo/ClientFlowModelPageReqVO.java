package cn.savas.hub.module.bpm.controller.admin.client.vo;

import cn.savas.hub.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/7 13:53
 */
@Data
public class ClientFlowModelPageReqVO extends PageParam {
    private Long id;

    @Schema(description = "流程模板名称")
    private String name;

    @Schema(description = "起否启用(1.启用 0.禁用)")
    private Integer enable;

    @Schema(description = "流程图")
    private String bpmnXml;
}
