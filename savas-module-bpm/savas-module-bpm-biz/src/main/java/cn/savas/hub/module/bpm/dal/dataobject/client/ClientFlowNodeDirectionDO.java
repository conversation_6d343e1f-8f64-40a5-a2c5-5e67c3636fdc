package cn.savas.hub.module.bpm.dal.dataobject.client;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@TableName("client_flow_node_direction")
@Data
public class ClientFlowNodeDirectionDO {
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long dirId;

    /**
     * 方案ID
     */
    private Long dirProjectId;

    /**
     * 业务ModelID
     */
    private Long dirHostModel;

    /**
     * 从哪个 node_ID
     */
    private Long dirSource;

    /**
     * 目标 node_ID
     */
    private Long dirTarget;

    /**
     *
     */
    private String dirDescription;

    /**
     *
     */
    private Integer dirStyle;

    /**
     *
     */
    private Integer dirCount;

    /**
     *
     */
    private Long dirState;
}
