package cn.savas.hub.module.bpm.dal.mysql.client;

import cn.savas.hub.framework.mybatis.core.mapper.BaseMapperX;
import cn.savas.hub.module.bpm.api.client.dto.ClientFlowStateDTO;
import cn.savas.hub.module.bpm.dal.dataobject.client.ClientFlowNodeDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/7 15:01
 */
@Mapper
public interface ClientFlowNodeMapper extends BaseMapperX<ClientFlowNodeDO> {

    default List<ClientFlowNodeDO> selectByPrjId(Long originalid){
        return selectList(
                new LambdaQueryWrapper<ClientFlowNodeDO>()
                        .eq(ClientFlowNodeDO::getNodeProjectId, originalid)
                        .orderByAsc(ClientFlowNodeDO::getNodeSortId)
        );
    }

    default List<ClientFlowNodeDO> getTaskProjectIds(Long loginUserId){
        return selectList(ClientFlowNodeDO::getNodeUserId, loginUserId);
    }

    List<ClientFlowStateDTO> selectPrjFlowState(@Param("originalids") List<Long> originalids);
}
