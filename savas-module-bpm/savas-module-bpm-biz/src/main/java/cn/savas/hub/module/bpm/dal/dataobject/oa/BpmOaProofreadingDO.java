package cn.savas.hub.module.bpm.dal.dataobject.oa;

import cn.savas.hub.framework.mybatis.core.dataobject.BaseDO;
import cn.savas.hub.module.bpm.enums.task.BpmTaskStatusEnum;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/22 14:15
 */
@TableName(value = "bpm_oa_proofreading", autoResultMap = true)
@KeySequence("bpm_oa_proofreading_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BpmOaProofreadingDO extends BaseDO {
    private Long id;
    private Long originalId;
    private Long engId;
    private String processInstanceId;
    private String actId;
    private String actName;
    private String actType;
    private String assignee;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, String> actVariable;
    /**
     * 审批结果
     *
     * 枚举 {@link BpmTaskStatusEnum}
     */
    private Integer status;
}
