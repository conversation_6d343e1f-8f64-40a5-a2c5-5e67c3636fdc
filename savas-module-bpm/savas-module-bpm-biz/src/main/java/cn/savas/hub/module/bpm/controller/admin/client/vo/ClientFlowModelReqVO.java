package cn.savas.hub.module.bpm.controller.admin.client.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/2/7 13:53
 */
@Data
public class ClientFlowModelReqVO {
    @Schema(description = "流程模板名称")
    @NotEmpty(message = "流程模板名称不能为空")
    private String name;

    @Schema(description = "起否启用(1.启用 0.禁用)")
    @NotNull(message = "启用状态不能为空")
    private Integer enable;

    @Schema(description = "流程图")
    private String bpmnXml;

    @Schema(description = "流程图SVG")
    private String bpmnSvg;

    @Schema(description = "流程标识")
    @NotEmpty(message = "流程标识不能为空")
    private String key;
}
