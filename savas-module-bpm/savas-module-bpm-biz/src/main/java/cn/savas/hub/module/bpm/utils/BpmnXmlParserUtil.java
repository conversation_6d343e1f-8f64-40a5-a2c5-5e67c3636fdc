package cn.savas.hub.module.bpm.utils;

import cn.savas.hub.module.bpm.enums.client.ClientWorkNodeStyleEnum;
import cn.savas.hub.module.bpm.enums.definition.BpmSimpleModelNodeType;
import cn.savas.hub.module.bpm.framework.flowable.core.enums.BpmTaskCandidateStrategyEnum;
import cn.savas.hub.module.bpm.framework.flowable.core.util.BpmnModelUtils;
import com.baomidou.mybatisplus.core.incrementer.DefaultIdentifierGenerator;
import lombok.Data;
import org.flowable.bpmn.converter.BpmnXMLConverter;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.springframework.stereotype.Component;

import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamReader;
import java.io.ByteArrayInputStream;
import java.util.*;
import java.util.stream.Collectors;

import static cn.savas.hub.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.savas.hub.module.bpm.enums.ErrorCodeConstants.CLIENT_NODE_USER_STRATEGY_NOT_ROLE;

/**
 * <AUTHOR>
 * @date 2025/2/7 15:51
 */
@Component
public class BpmnXmlParserUtil {
    private final BpmnXMLConverter bpmnXMLConverter = new BpmnXMLConverter();

    @Data
    public static class ClientWorkFlowNode {
        private Long id;
        private Long pid;
        private String name;
        private Integer style;
        private Long roleId;
        private Integer sortId;
        private String flowNodeType;
    }

    @Data
    public static class ClientWorkFlowDirection {
        private Long id;
        private String description;
        private Integer style;
        private Long sourceRef;
        private Long targetRef;
    }

    /**
     * 解析BPMN XML字符串
     * @param bpmnXml BPMN XML字符串
     * @return 解析结果，包含节点和连线信息
     */
    public BpmnParseResult parseBpmnXml(String bpmnXml) {
        BpmnParseResult result = new BpmnParseResult();
        BpmnModel bpmnModel;
        try {
            // 使用XMLStreamReader读取XML
            XMLInputFactory xif = XMLInputFactory.newInstance();
            XMLStreamReader xtr = xif.createXMLStreamReader(new ByteArrayInputStream(bpmnXml.getBytes()));
            // 将XML转换为BpmnModel
            bpmnModel = bpmnXMLConverter.convertToBpmnModel(xtr);
        } catch (Exception e) {
            throw new RuntimeException("Failed to parse BPMN XML: " + e.getMessage(), e);
        }
        // 获取主流程
        if (bpmnModel == null || bpmnModel.getProcesses().isEmpty()) {
            throw new RuntimeException("Invalid BPMN XML: No process found");
        }
        Process mainProcess = bpmnModel.getMainProcess();
        Collection<FlowElement> flowElements = mainProcess.getFlowElements();
        // 生成替换ID
        Map<String, Long> numbIdMap = new HashMap<>(flowElements.size());
        DefaultIdentifierGenerator identifierGenerator = DefaultIdentifierGenerator.getInstance();
        for (FlowElement element : flowElements) {
            numbIdMap.put(element.getId(), identifierGenerator.nextId(null));
        }
        // 获取子流程ID集合
        Set<String> subProcessIdSet = flowElements.stream()
                .filter(e -> e instanceof SubProcess)
                .map(FlowElement::getId)
                .collect(Collectors.toSet());
        // 处理流程元素
        processFlowElements(flowElements, result, subProcessIdSet, numbIdMap);
        return result;
    }

    /**
     * 处理流程元素
     * @param flowElements 流程元素集合
     * @param result 解析结果
     * @param subProcessIdSet 子流程ID集合
     */
    private void processFlowElements(Collection<FlowElement> flowElements, BpmnParseResult result, Set<String> subProcessIdSet, Map<String, Long> numbIdMap) {
        // 顺序号
        int sortId = 0;
        for (FlowElement element : flowElements) {
            if (element instanceof FlowNode) {
                processFlowNode((FlowNode) element, result, sortId++, numbIdMap);
            } else if (element instanceof SequenceFlow) {
                processSequenceFlow((SequenceFlow) element, result, subProcessIdSet, numbIdMap);
            }
        }
    }

    /**
     * 处理流程节点
     * @param flowNode 流程节点
     * @param result 解析结果
     */
    private void processFlowNode(FlowNode flowNode, BpmnParseResult result, int sortId, Map<String, Long> numbIdMap) {
        if (flowNode instanceof UserTask) {
            handleUserTask((UserTask) flowNode, result, null, sortId, numbIdMap);
        } else if (flowNode instanceof SubProcess) {
            handleSubProcess((SubProcess) flowNode, result, sortId, numbIdMap);
        } else if (flowNode instanceof StartEvent) {
            handleStartEvent((StartEvent) flowNode, result, sortId, numbIdMap);
        } else if (flowNode instanceof EndEvent) {
            handleEndEvent((EndEvent) flowNode, result, sortId, numbIdMap);
        }
    }


    /**
     * 处理用户任务
     * @param userTask
     * @param result
     * @param pid
     */
    private void handleUserTask(UserTask userTask, BpmnParseResult result, Long pid, int sortId, Map<String, Long> numbIdMap) {
        ClientWorkFlowNode node = new ClientWorkFlowNode();
        node.setId(numbIdMap.get(userTask.getId()));
        node.setName(userTask.getName());
        node.setPid(pid);
        node.setStyle(ClientWorkNodeStyleEnum.USER_TASK.getStyle());
        // 校验审批人策略
        validateCandidateStrategy(userTask);
        // 提取审批人角色ID
        node.setRoleId(extractRoleId(userTask));
        node.setSortId(sortId);
        node.setFlowNodeType(BpmSimpleModelNodeType.START_USER_NODE.getBpmnType());
        result.getNodes().add(node);
    }

    /**
     * 审批人策略校验（暂时客户端只支持角色）
     * @param element
     */
    private void validateCandidateStrategy(FlowElement element) {
        Integer strategy = BpmnModelUtils.parseCandidateStrategy(element);
        if (!BpmTaskCandidateStrategyEnum.ROLE.getStrategy().equals(strategy)) {
            throw exception(CLIENT_NODE_USER_STRATEGY_NOT_ROLE);
        }
    }

    /**
     * 提取客户端流程模版ID
     */
    private Long extractClientWorkId(FlowElement element){
        return Long.valueOf(BpmnModelUtils.parseClientWorkId(element));
    }
    /**
     * 提取审批人角色ID
     * @param element
     * @return
     */
    private Long extractRoleId(FlowElement element) {
        return Long.valueOf(BpmnModelUtils.parseCandidateParam(element));
    }

    /**
     * 处理子流程
     * @param subProcess
     * @param result
     */
    private void handleSubProcess(SubProcess subProcess, BpmnParseResult result, int sortId, Map<String, Long> numbIdMap) {
        ClientWorkFlowNode subProcessNode = createSubProcessNode(subProcess, numbIdMap);
        subProcessNode.setSortId(sortId);
        result.getNodes().add(subProcessNode);
        subProcess.getFlowElements().stream()
                .filter(UserTask.class::isInstance)
                .map(UserTask.class::cast)
                .forEach(userTask -> handleUserTask(userTask, result, numbIdMap.get(subProcess.getId()), sortId, numbIdMap));
    }

    /**
     * 创建子流程节点
     * @param subProcess
     * @return
     */
    private ClientWorkFlowNode createSubProcessNode(SubProcess subProcess, Map<String, Long> numbIdMap) {
        ClientWorkFlowNode node = new ClientWorkFlowNode();
        node.setId(numbIdMap.get(subProcess.getId()));
        node.setName(subProcess.getName());
        node.setStyle(ClientWorkNodeStyleEnum.USER_TASK.getStyle());
        node.setFlowNodeType(BpmSimpleModelNodeType.SUB_PROCESS_NODE.getBpmnType());
        return node;
    }

    /**
     * 处理开始事件
     * @param startEvent
     * @param result
     */
    private void handleStartEvent(StartEvent startEvent, BpmnParseResult result, int sortId, Map<String, Long> numbIdMap) {
        ClientWorkFlowNode node = new ClientWorkFlowNode();
        node.setId(numbIdMap.get(startEvent.getId()));
        node.setName(startEvent.getName());
        node.setStyle(ClientWorkNodeStyleEnum.START.getStyle());
        node.setSortId(sortId);
        node.setFlowNodeType(BpmSimpleModelNodeType.START_NODE.getBpmnType());
        result.getNodes().add(node);
    }

    /**
     * 处理结束事件
     * @param endEvent
     * @param result
     */
    private void handleEndEvent(EndEvent endEvent, BpmnParseResult result, int sortId, Map<String, Long> numbIdMap) {
        ClientWorkFlowNode node = new ClientWorkFlowNode();
        node.setId(numbIdMap.get(endEvent.getId()));
        node.setName(endEvent.getName());
        node.setStyle(ClientWorkNodeStyleEnum.END.getStyle());
        node.setSortId(sortId);
        node.setFlowNodeType(BpmSimpleModelNodeType.END_NODE.getBpmnType());
        result.getNodes().add(node);
    }

    /**
     * 处理连线数据
     * @param sequenceFlow
     * @param result
     * @param subProcessIdSet
     */
    private void processSequenceFlow(SequenceFlow sequenceFlow, BpmnParseResult result, Set<String> subProcessIdSet, Map<String, Long> numbIdMap) {
        ClientWorkFlowDirection connection = new ClientWorkFlowDirection();
        connection.setId(numbIdMap.get(sequenceFlow.getId()));
        connection.setDescription(sequenceFlow.getName());
        connection.setSourceRef(numbIdMap.get(sequenceFlow.getSourceRef()));
        connection.setTargetRef(numbIdMap.get(sequenceFlow.getTargetRef()));
        connection.setStyle(calculateConnectionStyle(sequenceFlow, subProcessIdSet));
        result.getConnections().add(connection);
    }

    /**
     * 返回方向的style（只有和子流程相连的方向是1）
     * @param flow
     * @param subProcessIdSet
     * @return
     */
    private int calculateConnectionStyle(SequenceFlow flow, Set<String> subProcessIdSet) {
        return subProcessIdSet.contains(flow.getSourceRef()) || subProcessIdSet.contains(flow.getTargetRef()) ? 1 : 0;
    }

    @Data
    public static class BpmnParseResult {
        private List<ClientWorkFlowNode> nodes = new ArrayList<>();
        private List<ClientWorkFlowDirection> connections = new ArrayList<>();
    }
}
