package cn.savas.hub.module.bpm.dal.dataobject.client;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@TableName("client_flow_node")
@Data
public class ClientFlowNodeDO {
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long nodeId;

    /**
     *
     */
    private Long nodePid;

    /**
     * Node模板ID
     */
    private Long nodeArchiveId;

    /**
     * 方案ID
     */
    private Long nodeProjectId;

    /**
     * 关联的业务ModelID
     */
    private Long nodeHostmodel;

    /**
     * 业务流程名字
     */
    private String nodeName;

    /**
     * 0 START 1 MAIN 2 ASSIST 3 ASSIST_SYNC 4 FINISHED
     */
    private Integer nodeStyle;

    /**
     * 主流程ID
     */
    private Long nodeMainId;

    /**
     * 角色ID
     */
    private Long nodeRoleId;

    /**
     * 执行人ID
     */
    private Long nodeUserId;

    /**
     * 执行人名称
     */
    private String nodeUserName;
    /**
     * 1 执行中 2 已提交 4 已经完成
     */
    private Long nodeState;

    /**
     *
     */
    private Integer nodeSortId;
}
