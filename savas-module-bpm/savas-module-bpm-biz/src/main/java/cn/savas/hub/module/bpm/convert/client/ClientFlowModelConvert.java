package cn.savas.hub.module.bpm.convert.client;

import cn.hutool.core.util.ZipUtil;
import cn.savas.hub.framework.common.client.enmus.EnumUnZipFieldNameV9;
import cn.savas.hub.module.bpm.api.client.dto.BpmnModelNodeRespDTO;
import cn.savas.hub.module.bpm.api.definition.dto.BpmnModelSequenceRespDTO;
import cn.savas.hub.module.bpm.controller.admin.client.vo.ClientFlowModelReqVO;
import cn.savas.hub.module.bpm.controller.admin.client.vo.ClientFlowModelRespVO;
import cn.savas.hub.module.bpm.controller.admin.client.vo.ClientFlowModelUpdateReqVO;
import cn.savas.hub.module.bpm.dal.dataobject.client.*;
import cn.savas.hub.module.bpm.utils.BpmnXmlParserUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/7 15:04
 */
@Mapper(imports = {EnumUnZipFieldNameV9.class, ZipUtil.class})
public interface ClientFlowModelConvert {
    ClientFlowModelConvert INSTANCE = Mappers.getMapper(ClientFlowModelConvert.class);

    @Mappings({
            @Mapping(target = "workName", source = "name"),
            @Mapping(target = "workState", source = "enable"),
    })
    ClientWorkflowDO convert(ClientFlowModelReqVO bean);

    @Mappings({
            @Mapping(target = "nodeId", source = "id"),
            @Mapping(target = "nodePid", source = "pid"),
            @Mapping(target = "nodeWorkFlow", ignore = true),
            @Mapping(target = "nodeName", source = "name"),
            @Mapping(target = "nodeStyle", source = "style"),
            @Mapping(target = "nodeRoleId", source = "roleId"),
            @Mapping(target = "nodeSortId", source = "sortId"),
    })
    ClientWorkflowNodeDO convert1(BpmnXmlParserUtil.ClientWorkFlowNode bean);
    List<ClientWorkflowNodeDO> convertList1(List<BpmnXmlParserUtil.ClientWorkFlowNode> beans);

    @Mappings({
            @Mapping(target = "dirId", source = "id"),
            @Mapping(target = "dirWorkFlow", ignore = true),
            @Mapping(target = "dirSource", source = "sourceRef"),
            @Mapping(target = "dirTarget", source = "targetRef"),
            @Mapping(target = "dirDescription", source = "description"),
            @Mapping(target = "dirStyle", source = "style"),
    })
    ClientWorkflowDirectionDO convert2(BpmnXmlParserUtil.ClientWorkFlowDirection bean);
    List<ClientWorkflowDirectionDO> convertList2(List<BpmnXmlParserUtil.ClientWorkFlowDirection> beans);

    @Mappings({
            @Mapping(target = "id", source = "nodeId"),
            @Mapping(target = "parentid", source = "nodePid"),
            @Mapping(target = "nodeName", source = "nodeName"),
            @Mapping(target = "style", source = "nodeStyle"),
    })
    BpmnModelNodeRespDTO convert3(ClientFlowNodeDO bean);
    List<BpmnModelNodeRespDTO> convertList3(List<ClientFlowNodeDO> beans);

    @Mappings({
            @Mapping(target = "id", source = "dirId"),
            @Mapping(target = "sourceflownodeid", source = "dirStyle"),
            @Mapping(target = "targetflownodeid", source = "dirTarget"),
            @Mapping(target = "style", source = "dirSource"),
    })
    BpmnModelSequenceRespDTO convert4(ClientFlowNodeDirectionDO bean);
    List<BpmnModelSequenceRespDTO> convertList4(List<ClientFlowNodeDirectionDO> beans);

    @Mappings({
            @Mapping(target = "id", source = "workId"),
            @Mapping(target = "name", source = "workName"),
            @Mapping(target = "enable", source = "workState"),
            @Mapping(target = "key", source = "workKey"),

    })
    ClientFlowModelRespVO convert5(ClientWorkflowDO bean);
    List<ClientFlowModelRespVO> convertList5(List<ClientWorkflowDO> beans);


    @Mappings({
            @Mapping(target = "workName", source = "name"),
            @Mapping(target = "workId", source = "id"),
            @Mapping(target = "workFlowChart", expression = "java(ZipUtil.zlib(bean.getBpmnSvg(), \"UTF-8\", 3))"),
    })
    ClientWorkflowDO convert6(ClientFlowModelUpdateReqVO bean);
}
