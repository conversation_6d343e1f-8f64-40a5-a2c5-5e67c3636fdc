package cn.savas.hub.module.bpm.api.client;

import cn.savas.hub.module.bpm.api.client.dto.BpmnModelNodeRespDTO;
import cn.savas.hub.module.bpm.convert.client.ClientFlowModelConvert;
import cn.savas.hub.module.bpm.dal.mysql.client.ClientFlowNodeDirMapper;
import cn.savas.hub.module.bpm.dal.mysql.client.ClientFlowNodeMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/22 18:09
 */
@Service
@Validated
public class BpmModelApiImpl implements BpmModelApi {
    @Resource
    private ClientFlowNodeMapper flowNodeMapper;
    @Resource
    private ClientFlowNodeDirMapper flowNodeDirMapper;

    @Override
    public List<BpmnModelNodeRespDTO> getFlowNodeTemplate(Long projectId) {
        return ClientFlowModelConvert.INSTANCE.convertList3(flowNodeMapper.selectByPrjId(projectId));
    }
}
