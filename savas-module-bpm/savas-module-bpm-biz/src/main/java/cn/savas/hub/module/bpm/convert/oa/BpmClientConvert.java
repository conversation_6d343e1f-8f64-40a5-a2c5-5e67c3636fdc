package cn.savas.hub.module.bpm.convert.oa;

import cn.savas.hub.module.bpm.api.client.dto.ClientFlowNodeDTO;
import cn.savas.hub.module.bpm.api.definition.dto.BpmProcessDefinitionInfoDTO;
import cn.savas.hub.module.bpm.api.client.dto.BpmClientHistoricReqDTO;
import cn.savas.hub.module.bpm.api.client.dto.BpmClientTaskDTO;
import cn.savas.hub.module.bpm.api.client.dto.BpmOaProofreadingDTO;
import cn.savas.hub.module.bpm.dal.dataobject.client.ClientFlowNodeDO;
import cn.savas.hub.module.bpm.dal.dataobject.definition.BpmProcessDefinitionInfoDO;
import cn.savas.hub.module.bpm.dal.dataobject.oa.BpmOaProofreadingDO;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/24 09:48
 */
@Mapper(imports = {cn.savas.hub.module.bpm.framework.flowable.core.util.FlowableUtils.class})
public interface BpmClientConvert {
    BpmClientConvert INSTANCE = Mappers.getMapper(BpmClientConvert.class);

    @Mappings({
        @Mapping(target = "nodeId", source = "taskDefinitionKey"),
        @Mapping(target = "nodeName", source = "name"),
        @Mapping(target = "startTime", source = "createTime"),
        @Mapping(target = "reason", expression = "java(FlowableUtils.getTaskReason(bean))"),
    })
    BpmClientHistoricReqDTO convert1(HistoricTaskInstance bean);
    List<BpmClientHistoricReqDTO> convertList1(List<HistoricTaskInstance> bean);


    BpmOaProofreadingDTO convert2(BpmOaProofreadingDO bean);

    List<BpmOaProofreadingDTO> convertList2(List<BpmOaProofreadingDO> bean);



    BpmClientTaskDTO convert3(Task bean);
    List<BpmClientTaskDTO> convertList3(List<Task> bean);


    BpmProcessDefinitionInfoDTO convert4(BpmProcessDefinitionInfoDO bean);


    ClientFlowNodeDTO convert5(ClientFlowNodeDO bean);

    List<ClientFlowNodeDTO> convertList5(List<ClientFlowNodeDO> bean);

}
