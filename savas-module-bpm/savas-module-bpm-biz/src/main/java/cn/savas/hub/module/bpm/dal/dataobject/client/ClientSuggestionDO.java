package cn.savas.hub.module.bpm.dal.dataobject.client;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@TableName("client_suggestion")
@Data
public class ClientSuggestionDO {
    /**
     *
     */
    @TableId
    private Long sugId;

    /**
     * 方案ID
     */
    private Long sugProjectId;

    /**
     * 工程ID， 具有流程Node的业务项
     */
    private Long sugHostmodel;

    /**
     * 业务Model ID
     */
    private Long sugModelId;

    /**
     * 建议节点
     */
    private Long sugSchedule;

    /**
     * 回复内容
     */
    private String sugComment;

    /**
     * 建议内容
     */
    private String sugContent;

    /**
     * 提交时间
     */
    private Date sugSubmitTime;

    /**
     * 回复时间
     */
    private Date sugCommentTime;

    /**
     * 创建人ID
     */
    private Long sugUserId;

    /**
     * 创建人名称
     */
    private String sugUserName;

    /**
     * 创建时间
     */
    private Date sugTime;

    /**
     * 顺序
     */
    private Integer sugSortId;

    /**
     *
     */
    private Long sugState;
}
