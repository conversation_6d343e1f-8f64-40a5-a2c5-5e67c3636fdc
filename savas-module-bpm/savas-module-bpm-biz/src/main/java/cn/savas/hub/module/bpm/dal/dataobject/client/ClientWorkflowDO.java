package cn.savas.hub.module.bpm.dal.dataobject.client;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@TableName("client_workflow")
@Data
public class ClientWorkflowDO {
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long workId;

    /**
     *
     */
    private String workName;

    /**
     *
     */
    private byte[] workFlowChart;

    /**
     *
     */
    private Long workUserId;

    /**
     *
     */
    private String workUserName;

    /**
     *
     */
    private Date workDate;

    /**
     *
     */
    private Long workState;

    /**
     * bpmn数据
     */
    private byte[] workByte;

    private String workKey;
}
