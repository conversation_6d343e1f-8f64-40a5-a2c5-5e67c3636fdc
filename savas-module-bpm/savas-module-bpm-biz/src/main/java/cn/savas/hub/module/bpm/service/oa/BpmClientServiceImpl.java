package cn.savas.hub.module.bpm.service.oa;

import cn.savas.hub.framework.common.enums.UserTypeEnum;
import cn.savas.hub.framework.websocket.core.sender.WebSocketMessageSender;
import cn.savas.hub.module.bpm.dal.dataobject.oa.BpmOaProofreadingDO;
import cn.savas.hub.module.bpm.dal.mysql.oa.BpmOaProofreadingMapper;
import org.flowable.task.api.Task;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/10/21 13:52
 */
@Service
public class BpmClientServiceImpl implements BpmClientService{
    @Resource
    private BpmOaProofreadingMapper oaProofreadingMapper;
    @Resource
    private WebSocketMessageSender webSocketMessageSender;

    /**
     * OA 流程定义 KEY
     */
    public static final String PROCESS_KEY = "process_compose_sub";

    @Override
    public void updateStatus(long id, Integer status) {
        oaProofreadingMapper.updateById(new BpmOaProofreadingDO().setId(id).setStatus(status));
    }

    @Override
    public void processTaskAssigned(Task task) {
        webSocketMessageSender.sendAck(UserTypeEnum.DESKTOP.getValue(), Long.valueOf(task.getAssignee()), "client-message-receive", "流程任务被分配");
    }
}
