package cn.savas.hub.module.bpm.controller.admin.oa;

import cn.savas.hub.module.bpm.service.oa.BpmClientService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/10/21 13:50
 */
@Tag(name = "管理后台 - OA 请假申请")
@RestController
@RequestMapping("/bpm/client")
public class BpmClientController {
    @Resource
    private BpmClientService clientService;


}
