package cn.savas.hub.module.bpm.dal.dataobject.client;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@TableName("client_flow_schedule")
@Data
public class ClientFlowScheduleDO {
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long schId;

    /**
     * 方案ID
     */
    private Long schProjectId;

    /**
     * 业务ModelID
     */
    private Long schHostModel;

    /**
     * 从哪个 node_ID
     */
    private Long schSource;

    /**
     * 目标 node_ID
     */
    private Long schTarget;

    /**
     *
     */
    private Date schStartDate;

    /**
     *
     */
    private Date schSubmitDate;

    /**
     *
     */
    private Long schState;

    /**
     *
     */
    private Integer schVersion;

    /**
     * 来源流程执行人ID
     */
    private Long schSourceUserId;

    /**
     * 来源流程执行人名称
     */
    private String schSourceUserName;

    /**
     * 目标流程执行人ID
     */
    private Long schTargetUserId;

    /**
     * 目标流程执行人名称
     */
    private String schTargetUserName;

    /**
     * 汇总文件版本
     */
    private Integer schSumFileVersion;
}
