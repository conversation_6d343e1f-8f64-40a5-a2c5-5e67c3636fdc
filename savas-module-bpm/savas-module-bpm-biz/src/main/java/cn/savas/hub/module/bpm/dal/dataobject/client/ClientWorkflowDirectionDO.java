package cn.savas.hub.module.bpm.dal.dataobject.client;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@TableName("client_workflow_direction")
@Data
public class ClientWorkflowDirectionDO {
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long dirId;

    /**
     * 业务ModelID
     */
    private Long dirWorkFlow;

    /**
     * 从哪个 node_ID
     */
    private Long dirSource;

    /**
     * 目标 node_ID
     */
    private Long dirTarget;

    /**
     *
     */
    private String dirDescription;

    /**
     *
     */
    private Integer dirStyle;
}
