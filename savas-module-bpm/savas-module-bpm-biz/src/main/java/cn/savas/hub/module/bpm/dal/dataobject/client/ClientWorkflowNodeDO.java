package cn.savas.hub.module.bpm.dal.dataobject.client;

import cn.savas.hub.module.bpm.enums.definition.BpmSimpleModelNodeType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("client_workflow_node")
public class ClientWorkflowNodeDO {
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long nodeId;

    /**
     *
     */
    private Long nodePid;

    /**
     * 流程模板D
     */
    private Long nodeWorkFlow;

    /**
     * 业务流程名字
     */
    private String nodeName;

    /**
     * 0 START 1 MAIN 2 ASSIST 3 ASSIST_SYNC 4 FINISHED
     */
    private Integer nodeStyle;

    /**
     * 角色ID
     */
    private Long nodeRoleId;

    /**
     *
     */
    private Integer nodeSortId;

    /**
     * 节点类型
     * @see BpmSimpleModelNodeType
     */
    private String nodeType;
}
