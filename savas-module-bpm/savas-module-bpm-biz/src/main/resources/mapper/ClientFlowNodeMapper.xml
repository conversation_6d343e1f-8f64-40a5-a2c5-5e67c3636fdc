<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.savas.hub.module.bpm.dal.mysql.client.ClientFlowNodeMapper">

    <select id="selectPrjFlowState" resultType="cn.savas.hub.module.bpm.api.client.dto.ClientFlowStateDTO">
        SELECT
            SUM(CASE WHEN node_style = 2 AND node_state = 64 THEN 1 ELSE 0 END) AS finishedCount,
            SUM(CASE WHEN node_style = 0 AND node_state > 64 THEN 1 ELSE 0 END) AS compilingCount,
            node_project_id
        FROM client_flow_node
        WHERE node_project_id in
            <foreach item="item" index="index" collection="originalids" open="(" separator="," close=")">
                #{item}
            </foreach>
        GROUP BY node_project_id
    </select>
</mapper>
