<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.savas.hub.module.bpm.dal.mysql.client.ClientFlowNodeDirMapper">

    <select id="selectByPrjIdAndSort"
            resultType="cn.savas.hub.module.bpm.dal.dataobject.client.ClientFlowNodeDirectionDO">
        SELECT
            cfnd.*
        FROM
            client_flow_node_direction cfnd
                LEFT JOIN client_flow_node cfn ON cfnd.dir_target = cfn.node_id
        WHERE
            cfnd.dir_project_id = #{originalid}
        ORDER BY
            cfn.node_sort_id ASC
    </select>
</mapper>
