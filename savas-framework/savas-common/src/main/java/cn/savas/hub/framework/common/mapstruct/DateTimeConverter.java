package cn.savas.hub.framework.common.mapstruct;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2024/12/6 14:38
 */
public class DateTimeConverter {
    public static LocalDateTime toLocalDateTime(String dateTimeStr) {
        if (dateTimeStr != null) {
            // 标准格式解析
            if (dateTimeStr.endsWith("Z")) {
                return ZonedDateTime.parse(dateTimeStr).toLocalDateTime();
            } else {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS");
                return LocalDateTime.parse(dateTimeStr, formatter);
            }
        }
        return null;
    }
}
