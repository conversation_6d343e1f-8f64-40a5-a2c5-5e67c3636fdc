package cn.savas.hub.framework.security.core.handler;

import cn.savas.hub.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.savas.hub.framework.common.pojo.CommonResult;
import cn.savas.hub.framework.common.util.servlet.ServletUtils;
import cn.savas.hub.framework.web.config.WebProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.access.ExceptionTranslationFilter;

import javax.annotation.Resource;
import javax.servlet.FilterChain;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

import static cn.savas.hub.framework.common.exception.enums.GlobalErrorCodeConstants.UNAUTHORIZED;

/**
 * 访问一个需要认证的 URL 资源，但是此时自己尚未认证（登录）的情况下，返回 {@link GlobalErrorCodeConstants#UNAUTHORIZED} 错误码，从而使前端重定向到登录页
 *
 * 补充：Spring Security 通过 {@link ExceptionTranslationFilter#sendStartAuthentication(HttpServletRequest, HttpServletResponse, FilterChain, AuthenticationException)} 方法，调用当前类
 *
 * <AUTHOR>
 */
@Slf4j
@SuppressWarnings("JavadocReference") // 忽略文档引用报错
public class AuthenticationEntryPointImpl implements AuthenticationEntryPoint {
    @Resource
    private WebProperties webProperties;
    private static final int ERROR_LEVEL = 4;

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException e) {
        log.debug("[commence][访问 URL({}) 时，没有登录]", request.getRequestURI(), e);

        String requestUri = request.getRequestURI();
        if (requestUri.startsWith(webProperties.getDesktopApi().getPrefix())) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("level", ERROR_LEVEL);
            errorResponse.put("msg", UNAUTHORIZED.getMsg());
            ServletUtils.writeJSON(response, errorResponse);
        } else {
            // 其他接口保持原有格式
            ServletUtils.writeJSON(response, CommonResult.error(UNAUTHORIZED));
        }
    }

}
