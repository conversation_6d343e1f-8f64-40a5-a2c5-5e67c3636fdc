package cn.savas.hub.framework.websocket.mq.message;

import cn.savas.hub.framework.mq.redis.core.pubsub.AbstractRedisChannelMessage;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * WebSocket消息重试的消息体
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class WebSocketRetryMessage extends AbstractRedisChannelMessage {

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 用户类型
     */
    private Integer userType;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 消息类型
     */
    private String messageType;

    /**
     * 消息内容
     */
    private String messageContent;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 创建时间
     */
    private Date createTime;
}
