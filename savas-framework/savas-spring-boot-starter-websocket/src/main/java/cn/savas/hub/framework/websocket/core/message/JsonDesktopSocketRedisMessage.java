package cn.savas.hub.framework.websocket.core.message;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

/**
 * JSON 格式的 WebSocket 消息帧
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class JsonDesktopSocketRedisMessage implements Serializable {
    private static final long serialVersionUID = -38087540995518948L;

    /**
     * 消息id
     */
    private String messageType;
    private String messageContent;
    private long timestamp;
}
