package cn.savas.hub.framework.websocket.core.sender;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.savas.hub.framework.common.util.json.JsonUtils;
import cn.savas.hub.framework.security.core.LoginUser;
import cn.savas.hub.framework.websocket.core.message.JsonDesktopSocketRedisMessage;
import cn.savas.hub.framework.websocket.core.message.JsonWebSocketMessage;
import cn.savas.hub.framework.websocket.core.session.WebSocketSessionManager;
import cn.savas.hub.framework.websocket.core.util.WebSocketFrameworkUtils;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * WebSocketMessageSender 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
public abstract class AbstractWebSocketMessageSender implements WebSocketMessageSender {
    private static final String OFFLINE_MESSAGES_KEY = "websocket:offline_messages:";
    protected final WebSocketSessionManager sessionManager;
    protected final RedisTemplate<String, String> redisTemplate;


    @Override
    public void send(Integer userType, Long userId, String messageType, String messageContent) {
        send(null, userType, userId, messageType, messageContent);
    }

    @Override
    public void send(Integer userType, String messageType, String messageContent) {
        send(null, userType, null, messageType, messageContent);
    }

    @Override
    public void send(String sessionId, String messageType, String messageContent) {
        send(sessionId, null, null, messageType, messageContent);
    }

    /**
     * 发送消息
     *
     * @param sessionId Session 编号
     * @param userType 用户类型
     * @param userId 用户编号
     * @param messageType 消息类型
     * @param messageContent 消息内容
     */
    public void send(String sessionId, Integer userType, Long userId, String messageType, String messageContent) {
        // 1. 获得 Session 列表
        List<WebSocketSession> sessions = Collections.emptyList();
        if (StrUtil.isNotEmpty(sessionId)) {
            WebSocketSession session = sessionManager.getSession(sessionId);
            if (session != null) {
                sessions = Collections.singletonList(session);
            }
        } else if (userType != null && userId != null) {
            sessions = (List<WebSocketSession>) sessionManager.getSessionList(userType, userId);
            if (CollUtil.isEmpty(sessions)) {
                this.asyncPersistMessage(userType, userId, messageType, messageContent);
            }
        } else if (userType != null) {
            sessions = (List<WebSocketSession>) sessionManager.getSessionList(userType);
        }
        if (CollUtil.isEmpty(sessions)) {
            log.info("[send][sessionId({}) userType({}) userId({}) messageType({}) messageContent({}) 未匹配到会话]",
                    sessionId, userType, userId, messageType, messageContent);
        }
        // 2. 执行发送
        doSend(sessions, messageType, messageContent);
    }

    /**
     * 发送消息的具体实现
     *
     * @param sessions Session 列表
     * @param messageType 消息类型
     * @param messageContent 消息内容
     */
    public void doSend(Collection<WebSocketSession> sessions, String messageType, String messageContent) {
        JsonWebSocketMessage message = new JsonWebSocketMessage().setType(messageType).setContent(messageContent);
        String payload = JsonUtils.toJsonString(message); // 关键，使用 JSON 序列化
        sessions.forEach(session -> {
            // 1. 各种校验，保证 Session 可以被发送
            if (session == null) {
                log.error("[doSend][session 为空, message({})]", message);
                return;
            }
            if (!session.isOpen()) {
                log.error("[doSend][session({}) 已关闭, message({})]", session.getId(), message);
                return;
            }
            // 2. 执行发送
            try {
                session.sendMessage(new TextMessage(payload));
                log.info("[doSend][session({}) 发送消息成功，message({})]", session.getId(), message);
            } catch (IOException ex) {
                log.error("[doSend][session({}) 发送消息失败，message({})]", session.getId(), message, ex);
            }
        });
    }

    /**
     * 持久化消息
     * @param userType
     * @param userId
     * @param messageType
     * @param messageContent
     */
    public void asyncPersistMessage(Integer userType, Long userId, String messageType, String messageContent) {
        try {
            String key = OFFLINE_MESSAGES_KEY + userId;
            // 序列化消息，包含元数据
            String messageJson = JSON.toJSONString(new JsonDesktopSocketRedisMessage(messageType, messageContent, System.currentTimeMillis()));
            redisTemplate.opsForList().leftPush(key, messageJson);
            // 限制最多 100 条消息
            redisTemplate.opsForList().trim(key, 0, 99);
            // 设置 7 天过期
            redisTemplate.expire(key, 7, TimeUnit.DAYS);
        } catch (Exception e) {
            log.error("[asyncPersist][userType({}) userId({}) 持久化消息失败]", userType, userId, e);
        }
    }

    /**
     * 离线消息的发送
     */
    @Override
    public void sendOfflineMessage(WebSocketSession session) {
        LoginUser user = WebSocketFrameworkUtils.getLoginUser(session);
        if (user != null) {
            // 发送离线消息
            String key = OFFLINE_MESSAGES_KEY + user.getId();
            List<String> messages = redisTemplate.opsForList().range(key, 0, -1);
            if (!CollUtil.isEmpty(messages)) {
                for (String messageJson : messages) {
                    JsonDesktopSocketRedisMessage message = JSON.parseObject(messageJson, JsonDesktopSocketRedisMessage.class);
                    send(user.getUserType(), user.getId(), message.getMessageType(), message.getMessageContent());
                }
                redisTemplate.delete(key); // 清理消息
            }
        }
    }



}
