package cn.savas.hub.framework.websocket.core.sender.local;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.savas.hub.framework.common.util.json.JsonUtils;
import cn.savas.hub.framework.websocket.core.message.JsonWebSocketMessage;
import cn.savas.hub.framework.websocket.core.sender.AbstractWebSocketMessageSender;
import cn.savas.hub.framework.websocket.core.sender.WebSocketMessageSender;
import cn.savas.hub.framework.websocket.core.session.WebSocketSessionManager;
import cn.savas.hub.framework.websocket.mq.message.WebSocketRetryMessage;
import cn.savas.hub.framework.websocket.mq.producer.WebSocketMessageProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 本地的 {@link WebSocketMessageSender} 实现类
 *
 * 使用消息队列实现消息确认机制，支持分布式场景
 *
 * <AUTHOR>
 */
@Slf4j
public class LocalWebSocketMessageSender extends AbstractWebSocketMessageSender {

    private final WebSocketMessageProducer webSocketMessageProducer;
    /**
     * 未确认消息记录，用于判断消息是否已确认
     */
    private final ConcurrentHashMap<String, ConcurrentHashMap<String, WebSocketRetryMessage>> pendingMessages = new ConcurrentHashMap<>();

    public LocalWebSocketMessageSender(WebSocketSessionManager sessionManager,
                                       WebSocketMessageProducer webSocketMessageProducer,
                                       RedisTemplate redisTemplate) {
        super(sessionManager, redisTemplate);
        this.webSocketMessageProducer = webSocketMessageProducer;
    }

    @Override
    public void sendAck(Integer userType, Long userId, String messageType, String messageContent) {
        // 1. 获得 Session 列表
        List<WebSocketSession> sessions = Collections.emptyList();
        if (userType != null && userId != null) {
            sessions = (List<WebSocketSession>) sessionManager.getSessionList(userType, userId);
            if (sessions.size() > 1) {
                sessions = sessions.subList(0, 1);
            }
            if (CollUtil.isEmpty(sessions)) {
                // 用户不在线，持久化消息
                log.info("[send][userType({}) userId({}) messageType({}) messageContent({}) 用户不在线，持久化消息]", userType, userId, messageType, messageContent);
                super.asyncPersistMessage(userType, userId, messageType, messageContent);
            }
        } else if (userType != null) {
            sessions = (List<WebSocketSession>) sessionManager.getSessionList(userType);
            if (CollUtil.isEmpty(sessions)) {
                log.info("[send][userType({}) userId({}) messageType({}) messageContent({}) 未匹配到会话]", userType, userId, messageType, messageContent);
            }
        }
        // 2. 执行发送
        doSend(sessions, messageType, messageContent, userType, userId);
    }


    /**
     * 发送消息的具体实现
     *
     * @param sessions Session 列表
     * @param messageType 消息类型
     * @param messageContent 消息内容
     */
    public void doSend(Collection<WebSocketSession> sessions, String messageType, String messageContent, Integer userType, Long userId) {
        JsonWebSocketMessage message = new JsonWebSocketMessage()
                .setType(messageType)
                .setContent(messageContent)
                .setMessageId(IdUtil.simpleUUID());
        String payload = JsonUtils.toJsonString(message); // 关键，使用 JSON 序列化
        sessions.forEach(session -> {
            // 1. 各种校验，保证 Session 可以被发送
            if (session == null) {
                log.error("[doSend][session 为空, message({})]", message);
                return;
            }
            if (!session.isOpen()) {
                log.error("[doSend][session({}) 已关闭, message({})]", session.getId(), message);
                return;
            }
            // 2. 执行发送
            try {
                session.sendMessage(new TextMessage(payload));
                // 3. 记录消息并设置确认超时
                registerPendingMessage(message.getMessageId(), session.getId(), userType, userId, message.getType(), message.getContent());
                log.info("[doSend][session({}) 发送消息成功，message({})]", session.getId(), message);
            } catch (IOException ex) {
                log.error("[doSend][session({}) 发送消息失败，message({})]", session.getId(), message, ex);
            }
        });
    }

    /**
     * 注册待确认消息，并发送到消息队列进行超时重试
     */
    private void registerPendingMessage(String messageId, String sessionId, Integer userType, Long userId, String messageType, String messageContent) {
        WebSocketRetryMessage retryMessage = new WebSocketRetryMessage()
                .setMessageId(messageId)
                .setSessionId(sessionId)
                .setUserType(userType)
                .setUserId(userId)
                .setMessageType(messageType)
                .setMessageContent(messageContent)
                .setRetryCount(0)
                .setCreateTime(new Date());

        // 本地记录待确认消息
        pendingMessages.putIfAbsent(sessionId, new ConcurrentHashMap<>());
        pendingMessages.get(sessionId).put(messageId, retryMessage);

        // 发送消息到队列，延迟队列会在超时后触发重试
        webSocketMessageProducer.sendRetryMessage(retryMessage);
    }

    /**
     * 处理消息确认
     */
    public void receiveAck(String sessionId, String messageId) {
        // 从本地记录中移除已确认的消息
        ConcurrentHashMap<String, WebSocketRetryMessage> clientMessages = pendingMessages.get(sessionId);
        if (clientMessages != null) {
            clientMessages.remove(messageId);
            if (clientMessages.isEmpty()) {
                pendingMessages.remove(sessionId);
            }
        }
    }

    /**
     * 处理消息重试，由消息队列消费者调用
     */
    public void retryMessage(WebSocketRetryMessage retryMessage) {
        String sessionId = retryMessage.getSessionId();
        String messageId = retryMessage.getMessageId();

        // 检查消息是否已经确认
        ConcurrentHashMap<String, WebSocketRetryMessage> sessionMessages = pendingMessages.get(sessionId);
        if (sessionMessages == null || !sessionMessages.containsKey(messageId)) {
            // 消息已确认，不需要重试
            return;
        }

        // 检查重试次数
        if (retryMessage.getRetryCount() >= 3) {
            log.error("消息发送失败，达到最大重试次数：{}", messageId);
            // 从本地记录中移除
            sessionMessages.remove(messageId);
            if (sessionMessages.isEmpty()) {
                pendingMessages.remove(sessionId);
            }
            return;
        }

        // 重试发送
        log.warn("ACK 超时，重新发送消息：{}, 重试次数：{}", messageId, retryMessage.getRetryCount() + 1);
        retryMessage.setRetryCount(retryMessage.getRetryCount() + 1);
        // 更新本地记录
        sessionMessages.put(messageId, retryMessage);

        // 获取会话并直接发送消息，避免创建新的待确认消息
        WebSocketSession session = sessionManager.getSession(sessionId);
        if (session != null && session.isOpen()) {
            try {
                JsonWebSocketMessage message = new JsonWebSocketMessage()
                        .setType(retryMessage.getMessageType())
                        .setContent(retryMessage.getMessageContent())
                        .setMessageId(messageId); // 使用原始消息ID

                String payload = JsonUtils.toJsonString(message);
                session.sendMessage(new TextMessage(payload));
                log.info("[retryMessage][session({}) 重试发送消息成功，message({})]", sessionId, message);

                // 重新放入队列，等待下次重试检查
                webSocketMessageProducer.sendRetryMessage(retryMessage);
            } catch (IOException ex) {
                log.error("[retryMessage][session({}) 重试发送消息失败，message({})]", sessionId, retryMessage, ex);
            }
        } else {
            log.error("[retryMessage][session({}) 已关闭或不存在，message({})]", sessionId, retryMessage);
            // 会话已关闭，清理消息记录
            sessionMessages.remove(messageId);
            if (sessionMessages.isEmpty()) {
                pendingMessages.remove(sessionId);
            }
        }
    }
}
