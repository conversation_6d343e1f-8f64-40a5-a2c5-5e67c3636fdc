package cn.savas.hub.framework.websocket.core.handler;

import cn.hutool.http.HttpUtil;
import cn.savas.hub.framework.common.util.json.JsonUtils;
import cn.savas.hub.framework.security.config.SecurityProperties;
import cn.savas.hub.framework.websocket.core.listener.WebSocketMessageListener;
import cn.savas.hub.framework.websocket.core.message.JsonDesktopSocketMessage;
import cn.savas.hub.framework.websocket.core.message.JsonWebSocketMessage;
import cn.savas.hub.framework.websocket.core.sender.local.LocalWebSocketMessageSender;
import cn.savas.hub.framework.websocket.core.session.WebSocketSessionManager;
import cn.savas.hub.framework.websocket.producer.SocketConnectErrorProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;

/**
 * JSON 格式 {@link WebSocketHandler} 实现类
 *
 * 基于 {@link JsonWebSocketMessage#getType()} 消息类型，调度到对应的 {@link WebSocketMessageListener} 监听器。
 *
 * <AUTHOR>
 */
@Slf4j
public class JsonDesktopSocketMessageHandler extends TextWebSocketHandler {
    private final LocalWebSocketMessageSender localWebSocketMessageSender;
    private final WebSocketSessionManager sessionManager;
    private final SecurityProperties securityProperties;
    private final SocketConnectErrorProducer socketConnectErrorProducer;
    @SuppressWarnings({"rawtypes", "unchecked"})
    public JsonDesktopSocketMessageHandler(WebSocketSessionManager sessionManager,
                                           LocalWebSocketMessageSender localWebSocketMessageSender,
                                           SecurityProperties securityProperties,
                                           SocketConnectErrorProducer socketConnectErrorProducer
    ) {
        this.sessionManager = sessionManager;
        this.localWebSocketMessageSender = localWebSocketMessageSender;
        this.securityProperties = securityProperties;
        this.socketConnectErrorProducer = socketConnectErrorProducer;
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        // 1.1 空消息，跳过
        if (message.getPayloadLength() == 0) {
            return;
        }
        // 1.2 ping 心跳消息，直接返回 pong 消息。
         if (message.getPayloadLength() == 4 && Objects.equals(message.getPayload(), "ping")) {
            session.sendMessage(new TextMessage("pong"));
            return;
        }
        // 2.1 解析消息
        JsonDesktopSocketMessage jsonMessage = null;
        try {
            jsonMessage = JsonUtils.parseObject(message.getPayload(), JsonDesktopSocketMessage.class);
        }catch (Exception e) {
            log.error("[handleTextMessage][session({}) message({}) 处理异常]", session.getId(), message.getPayload());
        }
        if (jsonMessage == null) {
            log.error("[handleTextMessage][session({}) message({}) 解析为空]", session.getId(), message.getPayload());
            return;
        }
        // 2.4 清除ack
        localWebSocketMessageSender.receiveAck(session.getId(), jsonMessage.getMessageId());
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) {
        Map<String, String> wsUriParamMap = HttpUtil.decodeParamMap(session.getUri().getQuery(), StandardCharsets.UTF_8);
        String token = wsUriParamMap.get(securityProperties.getTokenParameter());
        if (token != null) {
            socketConnectErrorProducer.sendSocketConnectError(token);
            sessionManager.removeSession(session);
        }

    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        // 发送离线消息
        localWebSocketMessageSender.sendOfflineMessage(session);
    }
}
