package cn.savas.hub.framework.websocket.mq.consumer;

import cn.savas.hub.framework.mq.redis.core.pubsub.AbstractRedisChannelMessageListener;
import cn.savas.hub.framework.websocket.core.sender.local.LocalWebSocketMessageSender;
import cn.savas.hub.framework.websocket.mq.message.WebSocketRetryMessage;
import lombok.extern.slf4j.Slf4j;

/**
 * WebSocket重试消息的消费者
 *
 * <AUTHOR>
 */
@Slf4j
public class WebSocketRetryConsumer extends AbstractRedisChannelMessageListener<WebSocketRetryMessage> {

    private final LocalWebSocketMessageSender localWebSocketMessageSender;

    public WebSocketRetryConsumer(LocalWebSocketMessageSender localWebSocketMessageSender) {
        this.localWebSocketMessageSender = localWebSocketMessageSender;
    }

    @Override
    public void onMessage(WebSocketRetryMessage message) {
        log.debug("[onMessage][消息内容({})]", message);
        try {
            // 交给发送者处理重试
            localWebSocketMessageSender.retryMessage(message);
        } catch (Exception e) {
            log.error("[onMessage][消息处理异常]", e);
        }
    }

}
