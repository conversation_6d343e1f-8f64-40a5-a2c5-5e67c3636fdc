package cn.savas.hub.framework.websocket.core.sender.local.confirm;

import java.util.Timer;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2024/11/11 15:32
 */

public class LocalMessageAck{
    private ConcurrentHashMap<String, ConcurrentHashMap<String, Timer>> pendingMessages = new ConcurrentHashMap<>();
    private ConcurrentHashMap<String, ConcurrentHashMap<String, Integer>> retryCount = new ConcurrentHashMap<>();

    public void registerPendingMessage(String sessionId, String messageId, Timer timer) {
        pendingMessages.putIfAbsent(sessionId, new ConcurrentHashMap<>());
        pendingMessages.get(sessionId).put(messageId, timer);

        retryCount.putIfAbsent(sessionId, new ConcurrentHashMap<>());
        retryCount.get(sessionId).putIfAbsent(messageId, 0);
    }

    public void receiveAck(String sessionId, String messageId) {
        ConcurrentHashMap<String, Timer> clientMessages = pendingMessages.get(sessionId);
        if (clientMessages != null) {
            Timer timer = clientMessages.remove(messageId);
            if (timer != null) {
                timer.cancel();
            }
            if (clientMessages.isEmpty()) {
                pendingMessages.remove(sessionId);
            }
        }
        ConcurrentHashMap<String, Integer> retryCounts = retryCount.get(sessionId);
        if (retryCounts != null) {
            retryCounts.remove(messageId);
        }
    }

    public ConcurrentHashMap<String, ConcurrentHashMap<String, Timer>> getPendingMessages() {
        return pendingMessages;
    }

    public int getRetryCount(String sessionId, String messageId) {
        return retryCount.getOrDefault(sessionId, new ConcurrentHashMap<>()).getOrDefault(messageId, 0);
    }

    public void incrementRetryCount(String sessionId, String messageId) {
        retryCount.get(sessionId).put(messageId, getRetryCount(sessionId, messageId) + 1);
    }

}
