package cn.savas.hub.framework.websocket.config;

import cn.savas.hub.framework.mq.redis.config.SavasRedisMQConsumerAutoConfiguration;
import cn.savas.hub.framework.mq.redis.core.RedisMQTemplate;
import cn.savas.hub.framework.security.config.SecurityProperties;
import cn.savas.hub.framework.websocket.core.handler.JsonDesktopSocketMessageHandler;
import cn.savas.hub.framework.websocket.core.handler.JsonWebSocketMessageHandler;
import cn.savas.hub.framework.websocket.core.listener.WebSocketMessageListener;
import cn.savas.hub.framework.websocket.core.security.LoginUserHandshakeInterceptor;
import cn.savas.hub.framework.websocket.core.security.WebSocketAuthorizeRequestsCustomizer;
import cn.savas.hub.framework.websocket.core.sender.WebSocketMessageSender;
import cn.savas.hub.framework.websocket.core.sender.kafka.KafkaWebSocketMessageConsumer;
import cn.savas.hub.framework.websocket.core.sender.kafka.KafkaWebSocketMessageSender;
import cn.savas.hub.framework.websocket.core.sender.local.LocalWebSocketMessageSender;
import cn.savas.hub.framework.websocket.core.sender.rabbitmq.RabbitMQWebSocketMessageConsumer;
import cn.savas.hub.framework.websocket.core.sender.rabbitmq.RabbitMQWebSocketMessageSender;
import cn.savas.hub.framework.websocket.core.sender.redis.RedisWebSocketMessageConsumer;
import cn.savas.hub.framework.websocket.core.sender.redis.RedisWebSocketMessageSender;
import cn.savas.hub.framework.websocket.core.sender.rocketmq.RocketMQWebSocketMessageConsumer;
import cn.savas.hub.framework.websocket.core.sender.rocketmq.RocketMQWebSocketMessageSender;
import cn.savas.hub.framework.websocket.core.session.WebSocketSessionHandlerDecorator;
import cn.savas.hub.framework.websocket.core.session.WebSocketSessionManager;
import cn.savas.hub.framework.websocket.core.session.WebSocketSessionManagerImpl;
import cn.savas.hub.framework.websocket.mq.consumer.WebSocketRetryConsumer;
import cn.savas.hub.framework.websocket.mq.producer.WebSocketMessageProducer;
import cn.savas.hub.framework.websocket.producer.SocketConnectErrorProducer;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.server.HandshakeInterceptor;

import java.util.List;

/**
 * WebSocket 自动配置
 *
 * <AUTHOR>
 */
@AutoConfiguration(before = SavasRedisMQConsumerAutoConfiguration.class) // before SavasRedisMQConsumerAutoConfiguration 的原因是，需要保证 RedisWebSocketMessageConsumer 先创建，才能创建 RedisMessageListenerContainer
@EnableWebSocket // 开启 websocket
@ConditionalOnProperty(prefix = "savas.websocket", value = "enable", matchIfMissing = true) // 允许使用 savas.websocket.enable=false 禁用 websocket
@EnableConfigurationProperties(WebSocketProperties.class)
public class SavasWebSocketAutoConfiguration {

    @Bean
    public WebSocketConfigurer webSocketConfigurer(HandshakeInterceptor[] handshakeInterceptors,
                                                   @Qualifier("webSocketHandler") WebSocketHandler webSocketHandler,
                                                   @Qualifier("desktopSocketHandler") WebSocketHandler desktopSocketHandler,
                                                   WebSocketProperties webSocketProperties) {
        return registry -> registry
                // 添加 WebSocketHandler
                .addHandler(webSocketHandler, webSocketProperties.getPath())
                .addHandler(desktopSocketHandler, webSocketProperties.getDesktopPath())
                .addInterceptors(handshakeInterceptors)
                // 允许跨域，否则前端连接会直接断开
                .setAllowedOriginPatterns("*");
    }

    @Bean
    public HandshakeInterceptor handshakeInterceptor() {
        return new LoginUserHandshakeInterceptor();
    }

    @Bean(name = "webSocketHandler")
    public WebSocketHandler webSocketHandler(WebSocketSessionManager sessionManager,
                                             List<? extends WebSocketMessageListener<?>> messageListeners,
                                             WebSocketMessageSender webSocketMessageSender
                                             ) {
        // 1. 创建 JsonWebSocketMessageHandler 对象，处理消息
        JsonWebSocketMessageHandler messageHandler = new JsonWebSocketMessageHandler(messageListeners);
        // 2. 创建 WebSocketSessionHandlerDecorator 对象，处理连接
        return new WebSocketSessionHandlerDecorator(messageHandler, sessionManager, webSocketMessageSender);
    }

    @Bean(name = "desktopSocketHandler")
    public WebSocketHandler desktopSocketHandler(WebSocketSessionManager sessionManager,
                                                 LocalWebSocketMessageSender localMessageAck,
                                                 WebSocketMessageSender webSocketMessageSender,
                                                 SecurityProperties securityProperties,
                                                 SocketConnectErrorProducer socketConnectErrorProducer
    ) {
        JsonDesktopSocketMessageHandler messageHandler = new JsonDesktopSocketMessageHandler(sessionManager, localMessageAck, securityProperties, socketConnectErrorProducer);
        return new WebSocketSessionHandlerDecorator(messageHandler, sessionManager, webSocketMessageSender);
    }

    @Bean
    public WebSocketSessionManager webSocketSessionManager() {
        return new WebSocketSessionManagerImpl();
    }

    @Bean
    public WebSocketAuthorizeRequestsCustomizer webSocketAuthorizeRequestsCustomizer(WebSocketProperties webSocketProperties) {
        return new WebSocketAuthorizeRequestsCustomizer(webSocketProperties);
    }

    // ==================== Sender 相关 ====================

    @Configuration
    @ConditionalOnProperty(prefix = "savas.websocket", name = "sender-type", havingValue = "local")
    public class LocalWebSocketMessageSenderConfiguration {
        @Bean
        public WebSocketMessageProducer webSocketMessageProducer(RedisMQTemplate redisMQTemplate) {
            return new WebSocketMessageProducer(redisMQTemplate);
        }
        @Bean
        public WebSocketRetryConsumer webSocketRetryConsumer(LocalWebSocketMessageSender localWebSocketMessageSender) {
            return new WebSocketRetryConsumer(localWebSocketMessageSender);
        }
        @Bean
        public LocalWebSocketMessageSender localWebSocketMessageSender(WebSocketSessionManager sessionManager, WebSocketMessageProducer webSocketMessageProducer, RedisTemplate redisTemplate) {
            return new LocalWebSocketMessageSender(sessionManager, webSocketMessageProducer, redisTemplate);
        }

    }

    @Configuration
    @ConditionalOnProperty(prefix = "savas.websocket", name = "sender-type", havingValue = "redis")
    public class RedisWebSocketMessageSenderConfiguration {
        @Bean
        public RedisWebSocketMessageSender redisWebSocketMessageSender(WebSocketSessionManager sessionManager,
                                                                       RedisMQTemplate redisMQTemplate) {
            return new RedisWebSocketMessageSender(sessionManager, redisMQTemplate);
        }

        @Bean
        public RedisWebSocketMessageConsumer redisWebSocketMessageConsumer(
                RedisWebSocketMessageSender redisWebSocketMessageSender) {
            return new RedisWebSocketMessageConsumer(redisWebSocketMessageSender);
        }

    }

    @Configuration
    @ConditionalOnProperty(prefix = "savas.websocket", name = "sender-type", havingValue = "rocketmq")
    public class RocketMQWebSocketMessageSenderConfiguration {

        @Bean
        public RocketMQWebSocketMessageSender rocketMQWebSocketMessageSender(
                WebSocketSessionManager sessionManager, RocketMQTemplate rocketMQTemplate,
                @Value("${savas.websocket.sender-rocketmq.topic}") String topic) {
            return new RocketMQWebSocketMessageSender(sessionManager, rocketMQTemplate, topic);
        }

        @Bean
        public RocketMQWebSocketMessageConsumer rocketMQWebSocketMessageConsumer(
                RocketMQWebSocketMessageSender rocketMQWebSocketMessageSender) {
            return new RocketMQWebSocketMessageConsumer(rocketMQWebSocketMessageSender);
        }

    }

    @Configuration
    @ConditionalOnProperty(prefix = "savas.websocket", name = "sender-type", havingValue = "rabbitmq")
    public class RabbitMQWebSocketMessageSenderConfiguration {

        @Bean
        public RabbitMQWebSocketMessageSender rabbitMQWebSocketMessageSender(
                WebSocketSessionManager sessionManager, RabbitTemplate rabbitTemplate,
                TopicExchange websocketTopicExchange) {
            return new RabbitMQWebSocketMessageSender(sessionManager, rabbitTemplate, websocketTopicExchange);
        }

        @Bean
        public RabbitMQWebSocketMessageConsumer rabbitMQWebSocketMessageConsumer(
                RabbitMQWebSocketMessageSender rabbitMQWebSocketMessageSender) {
            return new RabbitMQWebSocketMessageConsumer(rabbitMQWebSocketMessageSender);
        }

        /**
         * 创建 Topic Exchange
         */
        @Bean
        public TopicExchange websocketTopicExchange(@Value("${savas.websocket.sender-rabbitmq.exchange}") String exchange) {
            return new TopicExchange(exchange,
                    true,  // durable: 是否持久化
                    false);  // exclusive: 是否排它
        }

    }

    @Configuration
    @ConditionalOnProperty(prefix = "savas.websocket", name = "sender-type", havingValue = "kafka")
    public class KafkaWebSocketMessageSenderConfiguration {

        @Bean
        public KafkaWebSocketMessageSender kafkaWebSocketMessageSender(
                WebSocketSessionManager sessionManager, KafkaTemplate<Object, Object> kafkaTemplate,
                @Value("${savas.websocket.sender-kafka.topic}") String topic) {
            return new KafkaWebSocketMessageSender(sessionManager, kafkaTemplate, topic);
        }

        @Bean
        public KafkaWebSocketMessageConsumer kafkaWebSocketMessageConsumer(
                KafkaWebSocketMessageSender kafkaWebSocketMessageSender) {
            return new KafkaWebSocketMessageConsumer(kafkaWebSocketMessageSender);
        }

    }


    @Bean
    public SocketConnectErrorProducer socketConnectErrorProducer(ApplicationContext applicationContext) {
        return new SocketConnectErrorProducer(applicationContext);
    }
}
