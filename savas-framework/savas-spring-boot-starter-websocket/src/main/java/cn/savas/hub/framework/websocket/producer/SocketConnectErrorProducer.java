package cn.savas.hub.framework.websocket.producer;

import cn.savas.hub.framework.websocket.producer.message.SocketConnectErrorEvent;
import org.springframework.context.ApplicationContext;

/**
 * <AUTHOR>
 * @date 2024/12/5 18:12
 */
public class SocketConnectErrorProducer {
    private final ApplicationContext applicationContext;

    public SocketConnectErrorProducer(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    public void sendSocketConnectError(String token) {
        applicationContext.publishEvent(new SocketConnectErrorEvent(token));
    }
}
