package cn.savas.hub.framework.websocket.mq.producer;

import cn.savas.hub.framework.mq.redis.core.RedisMQTemplate;
import cn.savas.hub.framework.websocket.mq.message.WebSocketRetryMessage;
import lombok.extern.slf4j.Slf4j;

/**
 * WebSocket消息生产者
 *
 * <AUTHOR>
 */
@Slf4j
public class WebSocketMessageProducer {

    /**
     * 消息队列模板
     */
    private final RedisMQTemplate redisMQTemplate;

    /**
     * 延迟时间（毫秒）
     */
    private static final int RETRY_DELAY_MS = 30 * 1000;

    public WebSocketMessageProducer(RedisMQTemplate redisMQTemplate) {
        this.redisMQTemplate = redisMQTemplate;
    }

    /**
     * 发送重试消息到队列
     *
     * @param message 重试消息
     */
    public void sendRetryMessage(WebSocketRetryMessage message) {
        try {
            // 使用延迟队列，设置消息延迟30秒后被消费
            redisMQTemplate.sendDelay( message, RETRY_DELAY_MS);
            log.debug("[sendRetryMessage][messageId({}) 发送成功]", message.getMessageId());
        } catch (Exception e) {
            log.error("[sendRetryMessage][messageId({}) 发送异常]", message.getMessageId(), e);
        }
    }
}
