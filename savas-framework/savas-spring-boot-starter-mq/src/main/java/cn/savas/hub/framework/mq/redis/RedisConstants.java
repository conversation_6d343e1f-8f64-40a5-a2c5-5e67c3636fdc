package cn.savas.hub.framework.mq.redis;

public class RedisConstants {

    /**
     * Redis Stream 默认的消费组名称
     */
    public static final String REDIS_STREAM_GROUP_NAME = "savas-group";

    /**
     * 判定消费者死亡的时间间隔
     * 3分钟
     */
    public static final long CONSUMER_DEFAULT_TIME = 60 * 1000 * 3;


    /**
     * 判定消费者死亡 空闲时间
     * 3分钟
     */
    public static final long CONSUMER_DEAD_TIME_FREE = 60 * 1000 * 3;

    /**
     * 死信队列名称
     */
    public static final String REDIS_STREAM_DEAD_LETTER_QUEUE_NAME = "dead-letter-queue";

}
