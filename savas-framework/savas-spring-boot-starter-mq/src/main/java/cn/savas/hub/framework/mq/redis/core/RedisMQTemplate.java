package cn.savas.hub.framework.mq.redis.core;

import cn.hutool.core.util.IdUtil;
import cn.savas.hub.framework.common.util.json.JsonUtils;
import cn.savas.hub.framework.mq.redis.core.interceptor.RedisMessageInterceptor;
import cn.savas.hub.framework.mq.redis.core.message.AbstractRedisMessage;
import cn.savas.hub.framework.mq.redis.core.pubsub.AbstractRedisChannelMessage;
import cn.savas.hub.framework.mq.redis.core.stream.AbstractRedisStreamMessage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import org.springframework.data.redis.connection.stream.RecordId;
import org.springframework.data.redis.connection.stream.StreamRecords;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;

/**
 * Redis MQ 操作模板类
 *
 * <AUTHOR>
 */
@AllArgsConstructor
public class RedisMQTemplate {

    @Getter
    private final RedisTemplate<String, String> redisTemplate;

    /**
     * 拦截器数组
     */
    @Getter
    private final List<RedisMessageInterceptor> interceptors = new ArrayList<>();

    private final ThreadPoolTaskScheduler taskScheduler;

    // 延迟队列的 zset key 前缀
    private static final String DELAY_QUEUE_PREFIX = "mq:delay:";
    // 消息内容的 key 前缀
    private static final String MESSAGE_CONTENT_PREFIX = "mq:delay:message:";
    // 轮询间隔，单位毫秒
    private static final long POLL_INTERVAL = 1000L;
    // 每次轮询处理的最大消息数
    private static final int BATCH_SIZE = 10;

    // 存储当前活跃的延迟队列轮询任务
    private final ConcurrentHashMap<String, ScheduledFuture<?>> delayQueueTasks = new ConcurrentHashMap<>();

    /**
     * 发送 Redis 消息，基于 Redis pub/sub 实现
     *
     * @param message 消息
     */
    public <T extends AbstractRedisChannelMessage> void send(T message) {
        try {
            sendMessageBefore(message);
            // 发送消息
            redisTemplate.convertAndSend(message.getChannel(), JsonUtils.toJsonString(message));
        } finally {
            sendMessageAfter(message);
        }
    }

    /**
     * 发送 Redis 消息，基于 Redis Stream 实现
     *
     * @param message 消息
     * @return 消息记录的编号对象
     */
    public <T extends AbstractRedisStreamMessage> RecordId send(T message) {
        try {
            sendMessageBefore(message);
            // 发送消息
            return redisTemplate.opsForStream().add(StreamRecords.newRecord()
                    .ofObject(JsonUtils.toJsonString(message)) // 设置内容
                    .withStreamKey(message.getStreamKey())); // 设置 stream key
        } finally {
            sendMessageAfter(message);
        }
    }

    /**
     * 添加拦截器
     *
     * @param interceptor 拦截器
     */
    public void addInterceptor(RedisMessageInterceptor interceptor) {
        interceptors.add(interceptor);
    }

    private void sendMessageBefore(AbstractRedisMessage message) {
        // 正序
        interceptors.forEach(interceptor -> interceptor.sendMessageBefore(message));
    }

    private void sendMessageAfter(AbstractRedisMessage message) {
        // 倒序
        for (int i = interceptors.size() - 1; i >= 0; i--) {
            interceptors.get(i).sendMessageAfter(message);
        }
    }

    /**
     * 发送延迟消息
     *
     * @param message 消息内容
     * @param delayMillis 延迟时间，单位毫秒
     */
    public <T extends AbstractRedisChannelMessage> void sendDelay(T message, long delayMillis) {
        try {
            // 1. 生成消息ID
            String messageId = IdUtil.fastSimpleUUID();

            // 2. 存储消息内容
            String messageKey = MESSAGE_CONTENT_PREFIX + messageId;
            DelayedMessage<T> delayedMessage = new DelayedMessage<>(message.getChannel(), message);
            String messageJson = JsonUtils.toJsonString(delayedMessage);
            redisTemplate.opsForValue().set(messageKey, messageJson);

            // 3. 将消息ID添加到延迟队列的zset中，score为消息的执行时间
            String delayQueueKey = DELAY_QUEUE_PREFIX + message.getChannel();
            double score = System.currentTimeMillis() + delayMillis;
            redisTemplate.opsForZSet().add(delayQueueKey, messageId, score);

            // 4. 确保该主题的延迟队列处理任务已启动
            startDelayQueueProcessor(message.getChannel());
        } catch (Exception e) {
            throw new RuntimeException("发送延迟消息失败", e);
        }
    }

    /**
     * 启动指定主题的延迟队列处理任务
     *
     * @param topic 主题
     */
    private void startDelayQueueProcessor(String topic) {
        String delayQueueKey = DELAY_QUEUE_PREFIX + topic;

        if (delayQueueTasks.containsKey(delayQueueKey)) {
            // 任务已存在，无需重复启动
            return;
        }

        // 创建并启动定时任务，定期检查延迟队列中是否有到期的消息
        ScheduledFuture<?> future = taskScheduler.scheduleWithFixedDelay(() -> {
            processDelayQueue(topic);
        }, new Date(), POLL_INTERVAL);

        delayQueueTasks.put(delayQueueKey, future);
    }

    /**
     * 处理指定主题的延迟队列
     *
     * @param topic 主题
     */
    private void processDelayQueue(String topic) {
        String delayQueueKey = DELAY_QUEUE_PREFIX + topic;

        try {
            // 获取当前时间作为score上限
            double maxScore = System.currentTimeMillis();

            // 查询已到期的消息
            Set<ZSetOperations.TypedTuple<String>> typedTuples = redisTemplate.opsForZSet()
                    .rangeByScoreWithScores(delayQueueKey, 0, maxScore, 0, BATCH_SIZE);

            if (typedTuples == null || typedTuples.isEmpty()) {
                return;
            }

            for (ZSetOperations.TypedTuple<String> tuple : typedTuples) {
                Object value = tuple.getValue();
                if (value == null) continue;

                String messageId = value.toString();
                String messageKey = MESSAGE_CONTENT_PREFIX + messageId;

                // 获取消息内容
                Object messageObj = redisTemplate.opsForValue().get(messageKey);
                if (messageObj == null) continue;

                String messageJson = messageObj.toString();
                DelayedMessage<?> delayedMessage = JsonUtils.parseObject(messageJson, DelayedMessage.class);

                if (delayedMessage != null) {
                    // 发送消息到实际的主题
                    redisTemplate.convertAndSend(delayedMessage.getTopic(),
                            JsonUtils.toJsonString(delayedMessage.getMessage()));

                    // 从延迟队列中移除消息
                    redisTemplate.opsForZSet().remove(delayQueueKey, messageId);

                    // 删除消息内容
                    redisTemplate.delete(messageKey);
                }
            }
        } catch (Exception e) {
            // 处理延迟队列过程中的异常不应该影响整个任务的执行
            // 记录日志即可
            System.err.println("处理延迟队列异常: " + e.getMessage());
        }
    }

    /**
     * 延迟消息包装类
     */
    @Data
    private static class DelayedMessage<T> {
        private String topic;
        private T message;

        public DelayedMessage() {
        }

        public DelayedMessage(String topic, T message) {
            this.topic = topic;
            this.message = message;
        }

        public String getTopic() {
            return topic;
        }

        public void setTopic(String topic) {
            this.topic = topic;
        }

        public T getMessage() {
            return message;
        }

        public void setMessage(T message) {
            this.message = message;
        }
    }

    /**
     * 停止所有延迟队列处理任务
     */
    public void shutdown() {
        for (ScheduledFuture<?> future : delayQueueTasks.values()) {
            if (future != null && !future.isCancelled()) {
                future.cancel(true);
            }
        }
        delayQueueTasks.clear();
    }
}
