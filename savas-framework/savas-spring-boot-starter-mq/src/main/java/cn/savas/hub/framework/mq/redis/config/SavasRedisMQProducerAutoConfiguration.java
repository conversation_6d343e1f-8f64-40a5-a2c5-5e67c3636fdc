package cn.savas.hub.framework.mq.redis.config;

import cn.savas.hub.framework.mq.redis.core.RedisMQTemplate;
import cn.savas.hub.framework.mq.redis.core.interceptor.RedisMessageInterceptor;
import cn.savas.hub.framework.redis.config.SavasRedisAutoConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.util.List;

/**
 * Redis 消息队列 Producer 配置类
 *
 * <AUTHOR>
 */
@Slf4j
@AutoConfiguration(after = SavasRedisAutoConfiguration.class)
public class SavasRedisMQProducerAutoConfiguration {

    @Bean
    public RedisMQTemplate redisMQTemplate(StringRedisTemplate redisTemplate,
                                           List<RedisMessageInterceptor> interceptors) {
        ThreadPoolTaskScheduler taskScheduler = new ThreadPoolTaskScheduler();
        taskScheduler.setThreadNamePrefix("redis-mq-producer-");
        taskScheduler.setPoolSize(10);
        taskScheduler.initialize();
        RedisMQTemplate redisMQTemplate = new RedisMQTemplate(redisTemplate, taskScheduler);
        // 添加拦截器
        interceptors.forEach(redisMQTemplate::addInterceptor);
        return redisMQTemplate;
    }

}
