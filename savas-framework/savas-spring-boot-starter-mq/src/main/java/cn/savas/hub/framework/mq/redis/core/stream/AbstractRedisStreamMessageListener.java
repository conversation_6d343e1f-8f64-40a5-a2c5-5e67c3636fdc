package cn.savas.hub.framework.mq.redis.core.stream;

import cn.hutool.core.util.TypeUtil;
import cn.savas.hub.framework.common.util.json.JsonUtils;
import cn.savas.hub.framework.mq.redis.RedisConstants;
import cn.savas.hub.framework.mq.redis.core.RedisMQTemplate;
import cn.savas.hub.framework.mq.redis.core.interceptor.RedisMessageInterceptor;
import cn.savas.hub.framework.mq.redis.core.message.AbstractRedisMessage;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.stream.ObjectRecord;
import org.springframework.data.redis.connection.stream.RecordId;
import org.springframework.data.redis.connection.stream.StreamRecords;
import org.springframework.data.redis.stream.StreamListener;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Redis Stream 监听器抽象类，用于实现集群消费
 *
 * @param <T> 消息类型。一定要填写噢，不然会报错
 * <AUTHOR>
 */
public abstract class AbstractRedisStreamMessageListener<T extends AbstractRedisStreamMessage>implements StreamListener<String, ObjectRecord<String, String>> {

    /**
     * 消息类型
     */
    private final Class<T> messageType;
    /**
     * Redis Channel
     */
    @Getter
    private final String streamKey;

    /**
     * RedisMQTemplate
     */
    @Setter
    private RedisMQTemplate redisMQTemplate;

    @SneakyThrows
    protected AbstractRedisStreamMessageListener() {
        this.messageType = getMessageClass();
        this.streamKey = messageType.getDeclaredConstructor().newInstance().getStreamKey();
    }

    @Override
    public void onMessage(ObjectRecord<String, String> message) {
        // 消费消息
        T messageObj = JsonUtils.parseObject(message.getValue(), messageType);
        try {
            consumeMessageBefore(messageObj);
            // 消费消息
            this.onMessage(messageObj);
        } catch (Exception e) {
            // TODO savas：需要额外考虑以下几个点：
            // 1. 处理异常的情况
            // 2. 发送日志；以及事务的结合
            // 3. 消费日志；以及通用的幂等性
            // 4. 消费失败的重试，https://zhuanlan.zhihu.com/p/60501638
            // 直接转入死信队列（无需重试）
            transferToFailingQueue(message, message.getId());
        } finally {
            // ack 消息消费完成
            redisMQTemplate.getRedisTemplate().opsForStream().acknowledge(RedisConstants.REDIS_STREAM_GROUP_NAME, message);
            consumeMessageAfter(messageObj);
        }
    }

    private void transferToFailingQueue(ObjectRecord<String, String> message, RecordId id) {
        // 保留原始消息内容，写入死信 Stream
        StreamRecords.newRecord().withId(id).ofObject(message.getValue()).withStreamKey(RedisConstants.REDIS_STREAM_DEAD_LETTER_QUEUE_NAME);
        redisMQTemplate.getRedisTemplate().opsForStream().add(StreamRecords.newRecord()
                .withId(id) // 保持原 ID
                .ofObject(message.getValue())
                .withStreamKey(RedisConstants.REDIS_STREAM_DEAD_LETTER_QUEUE_NAME));
    }

    /**
     * 处理消息
     *
     * @param message 消息
     */
    public abstract void onMessage(T message);

    /**
     * 通过解析类上的泛型，获得消息类型
     *
     * @return 消息类型
     */
    @SuppressWarnings("unchecked")
    private Class<T> getMessageClass() {
        Type type = TypeUtil.getTypeArgument(getClass(), 0);
        if (type == null) {
            throw new IllegalStateException(String.format("类型(%s) 需要设置消息类型", getClass().getName()));
        }
        return (Class<T>) type;
    }

    private void consumeMessageBefore(AbstractRedisMessage message) {
        assert redisMQTemplate != null;
        List<RedisMessageInterceptor> interceptors = redisMQTemplate.getInterceptors();
        // 正序
        interceptors.forEach(interceptor -> interceptor.consumeMessageBefore(message));
    }

    private void consumeMessageAfter(AbstractRedisMessage message) {
        assert redisMQTemplate != null;
        List<RedisMessageInterceptor> interceptors = redisMQTemplate.getInterceptors();
        // 倒序
        for (int i = interceptors.size() - 1; i >= 0; i--) {
            interceptors.get(i).consumeMessageAfter(message);
        }
    }

}
